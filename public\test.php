<?php
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "PHP is working!<br>";
echo "Current directory: " . __DIR__ . "<br>";
echo "File exists check:<br>";
echo "autoload.php: " . (file_exists(__DIR__ . '/../bootstrap/autoload.php') ? 'YES' : 'NO') . "<br>";
echo "App.php: " . (file_exists(__DIR__ . '/../app/core/App.php') ? 'YES' : 'NO') . "<br>";

try {
    require_once __DIR__ . '/../bootstrap/autoload.php';
    echo "Autoload successful<br>";
    
    use App\Core\Router;
    use App\Core\Request;
    use App\Core\Response;
    
    echo "Classes loaded successfully<br>";
    
    $request = new Request();
    $response = new Response();
    echo "Request and Response created<br>";
    
    $router = new Router($request, $response);
    echo "Router created<br>";
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "<br>";
    echo "File: " . $e->getFile() . "<br>";
    echo "Line: " . $e->getLine() . "<br>";
}
?>
