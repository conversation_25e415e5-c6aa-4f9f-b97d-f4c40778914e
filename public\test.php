<?php
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h2>PHP Debug Information</h2>";
echo "PHP is working!<br>";
echo "Current directory: " . __DIR__ . "<br>";
echo "Document root: " . $_SERVER['DOCUMENT_ROOT'] . "<br>";
echo "Request URI: " . $_SERVER['REQUEST_URI'] . "<br>";

echo "<h3>File exists check:</h3>";
echo "autoload.php: " . (file_exists(__DIR__ . '/../bootstrap/autoload.php') ? 'YES' : 'NO') . "<br>";
echo "App.php: " . (file_exists(__DIR__ . '/../app/core/App.php') ? 'YES' : 'NO') . "<br>";
echo "HomeController.php: " . (file_exists(__DIR__ . '/../app/controllers/HomeController.php') ? 'YES' : 'NO') . "<br>";

echo "<h3>Testing autoload and classes:</h3>";
try {
    require_once __DIR__ . '/../bootstrap/autoload.php';
    echo "✓ Autoload successful<br>";

    $request = new App\Core\Request();
    echo "✓ Request created<br>";

    $response = new App\Core\Response();
    echo "✓ Response created<br>";

    $router = new App\Core\Router($request, $response);
    echo "✓ Router created<br>";

    $homeController = new App\Controllers\HomeController();
    echo "✓ HomeController created<br>";

    echo "<h3>Testing routing:</h3>";
    echo "Current path: " . $request->getPath() . "<br>";
    echo "Current method: " . $request->method() . "<br>";

} catch (Exception $e) {
    echo "<h3 style='color: red;'>Error occurred:</h3>";
    echo "Error: " . $e->getMessage() . "<br>";
    echo "File: " . $e->getFile() . "<br>";
    echo "Line: " . $e->getLine() . "<br>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
} catch (Error $e) {
    echo "<h3 style='color: red;'>Fatal Error occurred:</h3>";
    echo "Error: " . $e->getMessage() . "<br>";
    echo "File: " . $e->getFile() . "<br>";
    echo "Line: " . $e->getLine() . "<br>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
}
?>
