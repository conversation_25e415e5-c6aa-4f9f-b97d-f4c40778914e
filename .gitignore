# JobSpace Environment Configuration

# Application
APP_NAME=JobSpace
APP_ENV=development
APP_DEBUG=true
APP_URL=http://localhost/jobspace

# Database
DB_HOST=localhost
DB_PORT=3306
DB_DATABASE=jobspace
DB_USERNAME=root
DB_PASSWORD=

# Security
APP_KEY=your-secret-key-here
CSRF_TOKEN_NAME=csrf_token

# Email (will be configured later)
MAIL_DRIVER=smtp
MAIL_HOST=smtp.gmail.com
MAIL_PORT=587
MAIL_USERNAME=
MAIL_PASSWORD=
MAIL_ENCRYPTION=tls

# Payment Gateways (will be configured later)
BKASH_APP_KEY=
BKASH_APP_SECRET=
NAGAD_MERCHANT_ID=
NAGAD_MERCHANT_KEY=

# Cache
CACHE_DRIVER=file
CACHE_TTL=3600

# Session
SESSION_LIFETIME=1800
SESSION_SECURE=false
SESSION_HTTP_ONLY=true
