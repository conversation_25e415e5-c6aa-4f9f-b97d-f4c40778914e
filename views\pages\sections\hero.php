<?php
// Hero section with modern design, SEO tags, and JavaScript
// Page title and description for SEO purposes
$pageTitle = 'JobSpace - ' . (defined('APP_NAME') ? APP_NAME : 'JobSpace');
$pageDescription = 'JobSpace is a platform that connects job seekers with top companies in the tech industry.';
$pageKeywords = 'JobSpace, jobs, tech, software, engineering, web, mobile, design, data science';
?>

<section class="hero-section bg-gradient-to-r from-blue-600 to-blue-800 text-white py-20">
    <div class="container mx-auto px-4">
        <div class="flex flex-wrap items-center">
            <div class="w-full lg:w-1/2 mb-12 lg:mb-0">
                <h1 class="text-4xl md:text-5xl font-bold mb-6">Find Your Dream Job in Tech Industry</h1>
                <p class="text-xl mb-8 text-blue-100">Connect with top companies, showcase your skills, and take the next step in your career journey.</p>
                <div class="flex flex-wrap gap-4">
                    <a href="/jobs" class="bg-white text-blue-600 px-8 py-3 rounded-full font-semibold hover:bg-blue-50 transition duration-300">Find Jobs</a>
                    <a href="/post-job" class="border-2 border-white text-white px-8 py-3 rounded-full font-semibold hover:bg-white hover:text-blue-600 transition duration-300">Post a Job</a>
                </div>
                
                <div class="mt-12 flex items-center gap-8">
                    <div>
                        <h4 class="text-3xl font-bold">10K+</h4>
                        <p class="text-blue-100">Active Jobs</p>
                    </div>
                    <div>
                        <h4 class="text-3xl font-bold">8K+</h4>
                        <p class="text-blue-100">Companies</p>
                    </div>
                    <div>
                        <h4 class="text-3xl font-bold">15K+</h4>
                        <p class="text-blue-100">Job Seekers</p>
                    </div>
                </div>
            </div>
            
            <div class="w-full lg:w-1/2">
                <div class="bg-white p-8 rounded-2xl shadow-xl">
                    <h3 class="text-2xl font-bold text-gray-800 mb-6">Quick Job Search</h3>
                    <form class="space-y-4">
                        <div>
                            <input type="text" placeholder="Job Title or Keywords" class="w-full px-4 py-3 rounded-lg border border-gray-300 focus:border-blue-500 focus:ring-2 focus:ring-blue-200">
                        </div>
                        <div>
                            <input type="text" placeholder="Location" class="w-full px-4 py-3 rounded-lg border border-gray-300 focus:border-blue-500 focus:ring-2 focus:ring-blue-200">
                        </div>
                        <div>
                            <select class="w-full px-4 py-3 rounded-lg border border-gray-300 focus:border-blue-500 focus:ring-2 focus:ring-blue-200">
                                <option value="">Select Category</option>
                                <option value="web">Web Development</option>
                                <option value="mobile">Mobile Development</option>
                                <option value="design">UI/UX Design</option>
                                <option value="data">Data Science</option>
                            </select>
                        </div>
                        <button type="submit" class="w-full bg-blue-600 text-white py-3 rounded-lg font-semibold hover:bg-blue-700 transition duration-300">Search Jobs</button>
                    </form>
                </div>
            </div>
        </div>
    </div>
</section> 

<script>
    // Add any specific JavaScript for the hero section here
    // For example, you can add a function to handle the search form submission
    document.querySelector('.hero-section form').addEventListener('submit', function(e) {
        e.preventDefault();
        // Handle form submission here
        // You can use AJAX to submit the form data to the server
        // Now add search functionality
        console.log('Search form submitted');
    });
</script>
