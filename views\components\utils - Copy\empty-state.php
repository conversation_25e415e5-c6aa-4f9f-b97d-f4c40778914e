<?php
/**
 * Empty State Component
 * Dynamic reusable empty state component
 * 
 * @param string $title - Empty state title
 * @param string $message - Empty state message
 * @param string $icon - Icon HTML or predefined icon name
 * @param array $actions - Action buttons array
 * @param string $image - Custom image URL
 * @param string $size - Component size: sm, md, lg
 * @param string $type - Predefined type: no-data, no-results, error, maintenance
 */

// Extract data from passed parameters
$title = $data['title'] ?? 'No data found';
$message = $data['message'] ?? 'There are no items to display at the moment.';
$icon = $data['icon'] ?? '';
$actions = $data['actions'] ?? [];
$image = $data['image'] ?? '';
$size = $data['size'] ?? 'md';
$type = $data['type'] ?? 'no-data';

// Size classes
$sizeClasses = [
    'sm' => [
        'container' => 'py-8',
        'icon' => 'w-12 h-12',
        'title' => 'text-lg',
        'message' => 'text-sm',
        'spacing' => 'space-y-3'
    ],
    'md' => [
        'container' => 'py-12',
        'icon' => 'w-16 h-16',
        'title' => 'text-xl',
        'message' => 'text-base',
        'spacing' => 'space-y-4'
    ],
    'lg' => [
        'container' => 'py-16',
        'icon' => 'w-20 h-20',
        'title' => 'text-2xl',
        'message' => 'text-lg',
        'spacing' => 'space-y-6'
    ]
];

$sizeConfig = $sizeClasses[$size] ?? $sizeClasses['md'];

// Predefined types with icons and colors
$typeConfig = [
    'no-data' => [
        'icon' => '<svg fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path></svg>',
        'color' => 'text-gray-400',
        'title' => 'No data available',
        'message' => 'There are no items to display at the moment.'
    ],
    'no-results' => [
        'icon' => '<svg fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path></svg>',
        'color' => 'text-gray-400',
        'title' => 'No results found',
        'message' => 'Try adjusting your search or filter criteria.'
    ],
    'error' => [
        'icon' => '<svg fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.34 16.5c-.77.833.192 2.5 1.732 2.5z"></path></svg>',
        'color' => 'text-red-400',
        'title' => 'Something went wrong',
        'message' => 'An error occurred while loading the data.'
    ],
    'maintenance' => [
        'icon' => '<svg fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path></svg>',
        'color' => 'text-yellow-400',
        'title' => 'Under maintenance',
        'message' => 'This feature is temporarily unavailable.'
    ],
    'no-access' => [
        'icon' => '<svg fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"></path></svg>',
        'color' => 'text-orange-400',
        'title' => 'Access restricted',
        'message' => 'You don\'t have permission to view this content.'
    ]
];

// Use predefined config or custom values
$config = $typeConfig[$type] ?? $typeConfig['no-data'];
$displayTitle = $title ?: $config['title'];
$displayMessage = $message ?: $config['message'];
$displayIcon = $icon ?: $config['icon'];
$iconColor = $config['color'];
?>

<div class="empty-state text-center <?= $sizeConfig['container'] ?>">
    <div class="max-w-md mx-auto <?= $sizeConfig['spacing'] ?>">
        
        <!-- Image or Icon -->
        <?php if ($image): ?>
            <div class="mb-4">
                <img src="<?= htmlspecialchars($image) ?>" 
                     alt="<?= htmlspecialchars($displayTitle) ?>"
                     class="mx-auto <?= $sizeConfig['icon'] ?> object-contain">
            </div>
        <?php elseif ($displayIcon): ?>
            <div class="mx-auto <?= $sizeConfig['icon'] ?> <?= $iconColor ?> mb-4">
                <?= $displayIcon ?>
            </div>
        <?php endif; ?>
        
        <!-- Title -->
        <h3 class="<?= $sizeConfig['title'] ?> font-semibold text-gray-900 mb-2">
            <?= htmlspecialchars($displayTitle) ?>
        </h3>
        
        <!-- Message -->
        <p class="<?= $sizeConfig['message'] ?> text-gray-500 mb-6">
            <?= htmlspecialchars($displayMessage) ?>
        </p>
        
        <!-- Actions -->
        <?php if (!empty($actions)): ?>
        <div class="flex flex-col sm:flex-row gap-3 justify-center">
            <?php foreach ($actions as $index => $action): ?>
                <?php
                $actionType = $action['type'] ?? 'secondary';
                $actionText = $action['text'] ?? 'Action';
                $actionHref = $action['href'] ?? '';
                $actionOnclick = $action['onclick'] ?? '';
                $actionIcon = $action['icon'] ?? '';
                
                // Button classes based on type
                $buttonClasses = 'inline-flex items-center px-4 py-2 border text-sm font-medium rounded-lg transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2';
                
                if ($actionType === 'primary') {
                    $buttonClasses .= ' border-transparent bg-blue-600 text-white hover:bg-blue-700 focus:ring-blue-500';
                } else {
                    $buttonClasses .= ' border-gray-300 bg-white text-gray-700 hover:bg-gray-50 focus:ring-blue-500';
                }
                ?>
                
                <?php if ($actionHref): ?>
                    <a href="<?= htmlspecialchars($actionHref) ?>" 
                       class="<?= $buttonClasses ?>">
                        <?php if ($actionIcon): ?>
                        <span class="mr-2"><?= $actionIcon ?></span>
                        <?php endif; ?>
                        <?= htmlspecialchars($actionText) ?>
                    </a>
                <?php else: ?>
                    <button type="button" 
                            class="<?= $buttonClasses ?>"
                            <?= $actionOnclick ? 'onclick="' . htmlspecialchars($actionOnclick) . '"' : '' ?>>
                        <?php if ($actionIcon): ?>
                        <span class="mr-2"><?= $actionIcon ?></span>
                        <?php endif; ?>
                        <?= htmlspecialchars($actionText) ?>
                    </button>
                <?php endif; ?>
            <?php endforeach; ?>
        </div>
        <?php endif; ?>
    </div>
</div>

<style>
.line-clamp-3 {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
}
</style>
