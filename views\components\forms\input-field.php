<?php
/**
 * Input Field Component
 * Dynamic reusable input field component
 * 
 * @param string $name - Input name attribute
 * @param string $type - Input type: text, email, password, number, tel, url, etc.
 * @param string $label - Field label
 * @param string $value - Input value
 * @param string $placeholder - Input placeholder
 * @param bool $required - Is field required
 * @param bool $disabled - Is field disabled
 * @param string $size - Field size: sm, md, lg
 * @param string $error - Error message
 * @param string $help - Help text
 * @param array $attributes - Additional HTML attributes
 * @param string $icon - Icon HTML or class
 * @param string $iconPosition - Icon position: left, right
 */

// Extract data from passed parameters
$name = $data['name'] ?? '';
$type = $data['type'] ?? 'text';
$label = $data['label'] ?? '';
$value = $data['value'] ?? '';
$placeholder = $data['placeholder'] ?? '';
$required = $data['required'] ?? false;
$disabled = $data['disabled'] ?? false;
$size = $data['size'] ?? 'md';
$error = $data['error'] ?? '';
$help = $data['help'] ?? '';
$attributes = $data['attributes'] ?? [];
$icon = $data['icon'] ?? '';
$iconPosition = $data['iconPosition'] ?? 'left';
$id = $data['id'] ?? $name;
$class = $data['class'] ?? '';

// Size classes
$sizeClasses = [
    'sm' => 'px-3 py-2 text-sm',
    'md' => 'px-4 py-2.5 text-sm',
    'lg' => 'px-4 py-3 text-base'
];

// Base input classes
$baseClasses = 'block w-full border rounded-lg transition-all duration-200 focus:outline-none focus:ring-2';

// State classes
$stateClasses = '';
if ($error) {
    $stateClasses = 'border-red-300 text-red-900 placeholder-red-300 focus:ring-red-500 focus:border-red-500';
} else {
    $stateClasses = 'border-gray-300 text-gray-900 placeholder-gray-400 focus:ring-blue-500 focus:border-blue-500';
}

if ($disabled) {
    $stateClasses .= ' bg-gray-50 cursor-not-allowed opacity-75';
} else {
    $stateClasses .= ' bg-white hover:border-gray-400';
}

// Icon classes
$iconClasses = $icon ? ($iconPosition === 'left' ? 'pl-10' : 'pr-10') : '';

// Combine all classes
$inputClasses = $baseClasses . ' ' . $stateClasses . ' ' . $sizeClasses[$size] . ' ' . $iconClasses;

if ($class) {
    $inputClasses .= ' ' . $class;
}

// Build attributes string
$attributesString = '';
foreach ($attributes as $key => $val) {
    $attributesString .= ' ' . htmlspecialchars($key) . '="' . htmlspecialchars($val) . '"';
}

// Generate unique ID
$fieldId = $id ?: 'input-' . uniqid();
?>

<div class="form-group mb-4">
    <!-- Label -->
    <?php if ($label): ?>
    <label for="<?= htmlspecialchars($fieldId) ?>" 
           class="block text-sm font-medium text-gray-700 mb-2">
        <?= htmlspecialchars($label) ?>
        <?php if ($required): ?>
        <span class="text-red-500 ml-1">*</span>
        <?php endif; ?>
    </label>
    <?php endif; ?>
    
    <!-- Input Container -->
    <div class="relative">
        <!-- Left Icon -->
        <?php if ($icon && $iconPosition === 'left'): ?>
        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            <span class="text-gray-400">
                <?= $icon ?>
            </span>
        </div>
        <?php endif; ?>
        
        <!-- Input Field -->
        <input type="<?= htmlspecialchars($type) ?>"
               id="<?= htmlspecialchars($fieldId) ?>"
               name="<?= htmlspecialchars($name) ?>"
               value="<?= htmlspecialchars($value) ?>"
               placeholder="<?= htmlspecialchars($placeholder) ?>"
               class="<?= $inputClasses ?>"
               <?= $required ? 'required' : '' ?>
               <?= $disabled ? 'disabled' : '' ?>
               <?= $attributesString ?>
               autocomplete="<?= $type === 'password' ? 'current-password' : ($type === 'email' ? 'email' : 'off') ?>">
        
        <!-- Right Icon -->
        <?php if ($icon && $iconPosition === 'right'): ?>
        <div class="absolute inset-y-0 right-0 pr-3 flex items-center">
            <span class="text-gray-400">
                <?= $icon ?>
            </span>
        </div>
        <?php endif; ?>
        
        <!-- Password Toggle (for password fields) -->
        <?php if ($type === 'password'): ?>
        <div class="absolute inset-y-0 right-0 pr-3 flex items-center">
            <button type="button" 
                    class="text-gray-400 hover:text-gray-600 focus:outline-none focus:text-gray-600"
                    onclick="togglePassword('<?= $fieldId ?>')">
                <svg id="eye-<?= $fieldId ?>" class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                </svg>
                <svg id="eye-off-<?= $fieldId ?>" class="w-5 h-5 hidden" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L3 3m6.878 6.878L21 21"></path>
                </svg>
            </button>
        </div>
        <?php endif; ?>
    </div>
    
    <!-- Error Message -->
    <?php if ($error): ?>
    <p class="mt-2 text-sm text-red-600" id="<?= $fieldId ?>-error">
        <svg class="inline w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
        </svg>
        <?= htmlspecialchars($error) ?>
    </p>
    <?php endif; ?>
    
    <!-- Help Text -->
    <?php if ($help && !$error): ?>
    <p class="mt-2 text-sm text-gray-500" id="<?= $fieldId ?>-help">
        <?= htmlspecialchars($help) ?>
    </p>
    <?php endif; ?>
</div>

<script>
function togglePassword(fieldId) {
    const input = document.getElementById(fieldId);
    const eyeIcon = document.getElementById('eye-' + fieldId);
    const eyeOffIcon = document.getElementById('eye-off-' + fieldId);
    
    if (input.type === 'password') {
        input.type = 'text';
        eyeIcon.classList.add('hidden');
        eyeOffIcon.classList.remove('hidden');
    } else {
        input.type = 'password';
        eyeIcon.classList.remove('hidden');
        eyeOffIcon.classList.add('hidden');
    }
}

// Real-time validation
document.addEventListener('DOMContentLoaded', function() {
    const input = document.getElementById('<?= $fieldId ?>');
    if (input) {
        input.addEventListener('input', function() {
            // Remove error styling on input
            const errorElement = document.getElementById('<?= $fieldId ?>-error');
            if (errorElement) {
                this.classList.remove('border-red-300', 'text-red-900', 'placeholder-red-300', 'focus:ring-red-500', 'focus:border-red-500');
                this.classList.add('border-gray-300', 'text-gray-900', 'placeholder-gray-400', 'focus:ring-blue-500', 'focus:border-blue-500');
            }
        });
        
        // Email validation
        <?php if ($type === 'email'): ?>
        input.addEventListener('blur', function() {
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            if (this.value && !emailRegex.test(this.value)) {
                this.classList.add('border-red-300', 'focus:ring-red-500', 'focus:border-red-500');
            }
        });
        <?php endif; ?>
        
        // Required field validation
        <?php if ($required): ?>
        input.addEventListener('blur', function() {
            if (!this.value.trim()) {
                this.classList.add('border-red-300', 'focus:ring-red-500', 'focus:border-red-500');
            }
        });
        <?php endif; ?>
    }
});
</script>
