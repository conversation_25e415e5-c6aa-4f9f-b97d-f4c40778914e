<?php
// Get all flash messages
$flashMessages = Session::getFlash();

if (!empty($flashMessages)):
?>
<div class="flash-messages fixed top-20 right-4 z-50 space-y-2" id="flash-container">
    <?php foreach ($flashMessages as $type => $message): ?>
        <?php
        // Define styles for different message types
        $styles = [
            'success' => 'bg-green-50 border-green-200 text-green-800',
            'error' => 'bg-red-50 border-red-200 text-red-800',
            'warning' => 'bg-yellow-50 border-yellow-200 text-yellow-800',
            'info' => 'bg-blue-50 border-blue-200 text-blue-800'
        ];
        
        $icons = [
            'success' => 'fas fa-check-circle text-green-500',
            'error' => 'fas fa-exclamation-circle text-red-500',
            'warning' => 'fas fa-exclamation-triangle text-yellow-500',
            'info' => 'fas fa-info-circle text-blue-500'
        ];
        
        $style = $styles[$type] ?? $styles['info'];
        $icon = $icons[$type] ?? $icons['info'];
        ?>
        
        <div class="flash-message border rounded-lg p-4 shadow-lg max-w-sm <?= $style ?> transform translate-x-full opacity-0 transition-all duration-300"
             data-type="<?= $type ?>">
            <div class="flex items-start">
                <div class="flex-shrink-0">
                    <i class="<?= $icon ?>"></i>
                </div>
                <div class="ml-3 flex-1">
                    <p class="text-sm font-medium">
                        <?= htmlspecialchars($message) ?>
                    </p>
                </div>
                <div class="ml-4 flex-shrink-0">
                    <button class="inline-flex text-gray-400 hover:text-gray-600 focus:outline-none"
                            onclick="closeFlashMessage(this)">
                        <i class="fas fa-times text-sm"></i>
                    </button>
                </div>
            </div>
            
            <!-- Progress bar for auto-dismiss -->
            <div class="mt-2 w-full bg-gray-200 rounded-full h-1">
                <div class="progress-bar h-1 rounded-full transition-all duration-5000 ease-linear w-full"
                     style="background-color: currentColor; opacity: 0.3;"></div>
            </div>
        </div>
    <?php endforeach; ?>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Show flash messages with animation
    const flashMessages = document.querySelectorAll('.flash-message');
    
    flashMessages.forEach((message, index) => {
        setTimeout(() => {
            message.classList.remove('translate-x-full', 'opacity-0');
            message.classList.add('translate-x-0', 'opacity-100');
            
            // Start progress bar animation
            const progressBar = message.querySelector('.progress-bar');
            if (progressBar) {
                setTimeout(() => {
                    progressBar.style.width = '0%';
                }, 100);
            }
            
            // Auto-dismiss after 5 seconds
            setTimeout(() => {
                closeFlashMessage(message.querySelector('button'));
            }, 5000);
            
        }, index * 200); // Stagger animations
    });
});

function closeFlashMessage(button) {
    const message = button.closest('.flash-message');
    message.classList.add('translate-x-full', 'opacity-0');
    
    setTimeout(() => {
        message.remove();
        
        // Remove container if no messages left
        const container = document.getElementById('flash-container');
        if (container && container.children.length === 0) {
            container.remove();
        }
    }, 300);
}

// Function to add new flash message dynamically (for AJAX)
function addFlashMessage(type, message) {
    let container = document.getElementById('flash-container');
    
    if (!container) {
        container = document.createElement('div');
        container.id = 'flash-container';
        container.className = 'flash-messages fixed top-20 right-4 z-50 space-y-2';
        document.body.appendChild(container);
    }
    
    const styles = {
        'success': 'bg-green-50 border-green-200 text-green-800',
        'error': 'bg-red-50 border-red-200 text-red-800',
        'warning': 'bg-yellow-50 border-yellow-200 text-yellow-800',
        'info': 'bg-blue-50 border-blue-200 text-blue-800'
    };
    
    const icons = {
        'success': 'fas fa-check-circle text-green-500',
        'error': 'fas fa-exclamation-circle text-red-500',
        'warning': 'fas fa-exclamation-triangle text-yellow-500',
        'info': 'fas fa-info-circle text-blue-500'
    };
    
    const style = styles[type] || styles['info'];
    const icon = icons[type] || icons['info'];
    
    const messageElement = document.createElement('div');
    messageElement.className = `flash-message border rounded-lg p-4 shadow-lg max-w-sm ${style} transform translate-x-full opacity-0 transition-all duration-300`;
    messageElement.innerHTML = `
        <div class="flex items-start">
            <div class="flex-shrink-0">
                <i class="${icon}"></i>
            </div>
            <div class="ml-3 flex-1">
                <p class="text-sm font-medium">${message}</p>
            </div>
            <div class="ml-4 flex-shrink-0">
                <button class="inline-flex text-gray-400 hover:text-gray-600 focus:outline-none"
                        onclick="closeFlashMessage(this)">
                    <i class="fas fa-times text-sm"></i>
                </button>
            </div>
        </div>
        <div class="mt-2 w-full bg-gray-200 rounded-full h-1">
            <div class="progress-bar h-1 rounded-full transition-all duration-5000 ease-linear w-full"
                 style="background-color: currentColor; opacity: 0.3;"></div>
        </div>
    `;
    
    container.appendChild(messageElement);
    
    // Animate in
    setTimeout(() => {
        messageElement.classList.remove('translate-x-full', 'opacity-0');
        messageElement.classList.add('translate-x-0', 'opacity-100');
        
        // Start progress bar
        const progressBar = messageElement.querySelector('.progress-bar');
        setTimeout(() => {
            progressBar.style.width = '0%';
        }, 100);
        
        // Auto-dismiss
        setTimeout(() => {
            closeFlashMessage(messageElement.querySelector('button'));
        }, 5000);
    }, 100);
}
</script>
<?php endif; ?>
