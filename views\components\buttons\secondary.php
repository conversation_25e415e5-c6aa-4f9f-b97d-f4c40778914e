<?php
/**
 * Secondary Button Component
 * Dynamic reusable secondary button component
 * 
 * @param string $text - Button text
 * @param string $type - Button type: button, submit, reset (default: button)
 * @param string $size - Button size: xs, sm, md, lg, xl (default: md)
 * @param string $href - Link URL (if provided, renders as <a> tag)
 * @param string $onclick - JavaScript onclick function
 * @param bool $disabled - Disable button (default: false)
 * @param bool $loading - Show loading state (default: false)
 * @param string $icon - Icon HTML or class
 * @param string $iconPosition - Icon position: left, right (default: left)
 * @param string $target - Link target (for <a> tag)
 * @param array $attributes - Additional HTML attributes
 */

// Extract data from passed parameters
$text = $data['text'] ?? 'Button';
$type = $data['type'] ?? 'button';
$size = $data['size'] ?? 'md';
$href = $data['href'] ?? '';
$onclick = $data['onclick'] ?? '';
$disabled = $data['disabled'] ?? false;
$loading = $data['loading'] ?? false;
$icon = $data['icon'] ?? '';
$iconPosition = $data['iconPosition'] ?? 'left';
$target = $data['target'] ?? '';
$attributes = $data['attributes'] ?? [];
$id = $data['id'] ?? '';
$class = $data['class'] ?? '';

// Size classes
$sizeClasses = [
    'xs' => 'px-2 py-1 text-xs',
    'sm' => 'px-3 py-1.5 text-sm',
    'md' => 'px-4 py-2 text-sm',
    'lg' => 'px-5 py-2.5 text-base',
    'xl' => 'px-6 py-3 text-lg'
];

// Base classes
$baseClasses = 'inline-flex items-center justify-center font-medium rounded-lg transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2';

// Secondary button classes
$secondaryClasses = 'bg-gray-100 text-gray-700 hover:bg-gray-200 active:bg-gray-300 border border-gray-300 shadow-sm hover:shadow-md';

// Disabled classes
$disabledClasses = 'opacity-50 cursor-not-allowed';

// Loading classes
$loadingClasses = 'cursor-wait';

// Combine all classes
$allClasses = $baseClasses . ' ' . $secondaryClasses . ' ' . $sizeClasses[$size];

if ($disabled || $loading) {
    $allClasses .= ' ' . $disabledClasses;
}

if ($loading) {
    $allClasses .= ' ' . $loadingClasses;
}

if ($class) {
    $allClasses .= ' ' . $class;
}

// Build attributes string
$attributesString = '';
if ($id) {
    $attributesString .= ' id="' . htmlspecialchars($id) . '"';
}

if ($onclick && !$disabled && !$loading) {
    $attributesString .= ' onclick="' . htmlspecialchars($onclick) . '"';
}

foreach ($attributes as $key => $value) {
    $attributesString .= ' ' . htmlspecialchars($key) . '="' . htmlspecialchars($value) . '"';
}

// Generate unique ID for loading state
$buttonId = $id ?: 'btn-secondary-' . uniqid();
?>

<?php if ($href && !$disabled && !$loading): ?>
    <!-- Render as link -->
    <a href="<?= htmlspecialchars($href) ?>" 
       class="<?= $allClasses ?>"
       <?= $target ? 'target="' . htmlspecialchars($target) . '"' : '' ?>
       <?= $attributesString ?>>
        
        <?php if ($loading): ?>
            <!-- Loading spinner -->
            <svg class="animate-spin -ml-1 mr-2 h-4 w-4 text-gray-600" fill="none" viewBox="0 0 24 24">
                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
            Loading...
        <?php else: ?>
            <?php if ($icon && $iconPosition === 'left'): ?>
                <span class="mr-2"><?= $icon ?></span>
            <?php endif; ?>
            
            <?= htmlspecialchars($text) ?>
            
            <?php if ($icon && $iconPosition === 'right'): ?>
                <span class="ml-2"><?= $icon ?></span>
            <?php endif; ?>
        <?php endif; ?>
    </a>
<?php else: ?>
    <!-- Render as button -->
    <button type="<?= htmlspecialchars($type) ?>" 
            class="<?= $allClasses ?>"
            <?= $disabled || $loading ? 'disabled' : '' ?>
            <?= $attributesString ?>>
        
        <?php if ($loading): ?>
            <!-- Loading spinner -->
            <svg class="animate-spin -ml-1 mr-2 h-4 w-4 text-gray-600" fill="none" viewBox="0 0 24 24">
                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
            Loading...
        <?php else: ?>
            <?php if ($icon && $iconPosition === 'left'): ?>
                <span class="mr-2"><?= $icon ?></span>
            <?php endif; ?>
            
            <?= htmlspecialchars($text) ?>
            
            <?php if ($icon && $iconPosition === 'right'): ?>
                <span class="ml-2"><?= $icon ?></span>
            <?php endif; ?>
        <?php endif; ?>
    </button>
<?php endif; ?>
