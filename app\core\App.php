<?php

namespace App\Core;

use App\Core\Router;
use App\Core\Request;
use App\Core\Response;

class App
{
    protected Router $router;
    protected Request $request;
    protected Response $response;

    public function __construct()
    {
        $this->request = new Request();
        $this->response = new Response();
        $this->router = new Router($this->request, $this->response);

        // Load routes
        require_once __DIR__ . '/../../routes/web.php';
    }

    public function run()
    {
        echo $this->router->resolve();
    }

    public function getRouter(): Router
    {
        return $this->router;
    }
}
