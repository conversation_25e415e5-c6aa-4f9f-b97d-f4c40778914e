<?php
namespace App\Core;

class Router {
    private array $routes = [];
    public Request $request;
    public Response $response;
    private static $instance;

    public function __construct(Request $request = null, Response $response = null)
    {
        $this->request = $request ?: new Request();
        $this->response = $response ?: new Response();
        self::$instance = $this;
    }

    public static function getInstance()
    {
        if (!self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    public function addGet(string $path, $callback)
    {
        $this->routes['get'][$path] = $callback;
    }

    public function addPost(string $path, $callback)
    {
        $this->routes['post'][$path] = $callback;
    }

    public function resolve()
    {
        $path = $this->request->getPath();
        $method = $this->request->method();
        $callback = $this->routes[$method][$path] ?? false;

        if ($callback === false) {
            $this->response->setStatusCode(404);
            return '404 Not Found';
        }

        if (is_string($callback)) {
            // Handle Controller@method format
            if (strpos($callback, '@') !== false) {
                [$controllerName, $methodName] = explode('@', $callback);
                $controllerClass = "App\\Controllers\\$controllerName";

                if (class_exists($controllerClass)) {
                    $controller = new $controllerClass();
                    if (method_exists($controller, $methodName)) {
                        return $controller->$methodName($this->request);
                    }
                }
            }
            return $callback;
        }

        if (is_array($callback)) {
            $controller = new $callback[0]();
            $method = $callback[1];
            return $controller->$method($this->request);
        }

        return call_user_func($callback, $this->request);
    }

    public static function get(string $path, $callback)
    {
        return self::getInstance()->addGet($path, $callback);
    }

    public static function post(string $path, $callback)
    {
        return self::getInstance()->addPost($path, $callback);
    }

    public function dispatch()
    {
        echo $this->resolve();
    }
}
