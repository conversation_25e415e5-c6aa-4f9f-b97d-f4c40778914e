<?php
/**
 * Loading Spinner Component
 * Dynamic reusable loading spinner component
 * 
 * @param string $size - Spinner size: xs, sm, md, lg, xl
 * @param string $color - Spinner color: blue, green, red, yellow, purple, gray
 * @param string $type - Spinner type: circle, dots, bars, pulse
 * @param string $text - Loading text (optional)
 * @param bool $overlay - Show as overlay (default: false)
 * @param string $position - Position: center, left, right (default: center)
 */

// Extract data from passed parameters
$size = $data['size'] ?? 'md';
$color = $data['color'] ?? 'blue';
$type = $data['type'] ?? 'circle';
$text = $data['text'] ?? '';
$overlay = $data['overlay'] ?? false;
$position = $data['position'] ?? 'center';

// Size classes
$sizeClasses = [
    'xs' => 'w-3 h-3',
    'sm' => 'w-4 h-4',
    'md' => 'w-6 h-6',
    'lg' => 'w-8 h-8',
    'xl' => 'w-12 h-12'
];

// Color classes
$colorClasses = [
    'blue' => 'text-blue-600',
    'green' => 'text-green-600',
    'red' => 'text-red-600',
    'yellow' => 'text-yellow-600',
    'purple' => 'text-purple-600',
    'gray' => 'text-gray-600',
    'white' => 'text-white'
];

// Position classes
$positionClasses = [
    'center' => 'justify-center',
    'left' => 'justify-start',
    'right' => 'justify-end'
];

$spinnerSize = $sizeClasses[$size] ?? $sizeClasses['md'];
$spinnerColor = $colorClasses[$color] ?? $colorClasses['blue'];
$spinnerPosition = $positionClasses[$position] ?? $positionClasses['center'];

// Container classes
$containerClasses = "flex items-center {$spinnerPosition}";

if ($overlay) {
    $containerClasses = "fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50";
}
?>

<div class="loading-spinner <?= $containerClasses ?>">
    <div class="flex flex-col items-center space-y-2">
        
        <?php if ($type === 'circle'): ?>
            <!-- Circle Spinner -->
            <svg class="animate-spin <?= $spinnerSize ?> <?= $spinnerColor ?>" fill="none" viewBox="0 0 24 24">
                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
            
        <?php elseif ($type === 'dots'): ?>
            <!-- Dots Spinner -->
            <div class="flex space-x-1">
                <div class="<?= str_replace(['w-', 'h-'], ['w-2 h-2', ''], $spinnerSize) ?> bg-current rounded-full animate-bounce <?= $spinnerColor ?>" style="animation-delay: 0ms;"></div>
                <div class="<?= str_replace(['w-', 'h-'], ['w-2 h-2', ''], $spinnerSize) ?> bg-current rounded-full animate-bounce <?= $spinnerColor ?>" style="animation-delay: 150ms;"></div>
                <div class="<?= str_replace(['w-', 'h-'], ['w-2 h-2', ''], $spinnerSize) ?> bg-current rounded-full animate-bounce <?= $spinnerColor ?>" style="animation-delay: 300ms;"></div>
            </div>
            
        <?php elseif ($type === 'bars'): ?>
            <!-- Bars Spinner -->
            <div class="flex space-x-1 items-end">
                <div class="w-1 bg-current animate-pulse <?= $spinnerColor ?>" style="height: 16px; animation-delay: 0ms;"></div>
                <div class="w-1 bg-current animate-pulse <?= $spinnerColor ?>" style="height: 20px; animation-delay: 150ms;"></div>
                <div class="w-1 bg-current animate-pulse <?= $spinnerColor ?>" style="height: 16px; animation-delay: 300ms;"></div>
                <div class="w-1 bg-current animate-pulse <?= $spinnerColor ?>" style="height: 12px; animation-delay: 450ms;"></div>
                <div class="w-1 bg-current animate-pulse <?= $spinnerColor ?>" style="height: 16px; animation-delay: 600ms;"></div>
            </div>
            
        <?php elseif ($type === 'pulse'): ?>
            <!-- Pulse Spinner -->
            <div class="<?= $spinnerSize ?> bg-current rounded-full animate-ping <?= $spinnerColor ?>"></div>
            
        <?php else: ?>
            <!-- Default Circle Spinner -->
            <svg class="animate-spin <?= $spinnerSize ?> <?= $spinnerColor ?>" fill="none" viewBox="0 0 24 24">
                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
        <?php endif; ?>
        
        <!-- Loading Text -->
        <?php if ($text): ?>
        <div class="text-sm font-medium <?= $overlay ? 'text-white' : 'text-gray-700' ?>">
            <?= htmlspecialchars($text) ?>
        </div>
        <?php endif; ?>
    </div>
</div>

<?php if ($overlay): ?>
<script>
// Prevent scrolling when overlay is active
document.body.style.overflow = 'hidden';

// Auto-remove overlay after 30 seconds (safety measure)
setTimeout(function() {
    const spinner = document.querySelector('.loading-spinner');
    if (spinner && spinner.classList.contains('fixed')) {
        spinner.remove();
        document.body.style.overflow = '';
    }
}, 30000);
</script>
<?php endif; ?>

<style>
@keyframes bounce {
    0%, 80%, 100% {
        transform: scale(0);
    }
    40% {
        transform: scale(1);
    }
}

.animate-bounce {
    animation: bounce 1.4s infinite ease-in-out both;
}

@keyframes pulse-custom {
    0% {
        transform: scale(1);
        opacity: 1;
    }
    50% {
        transform: scale(1.1);
        opacity: 0.7;
    }
    100% {
        transform: scale(1);
        opacity: 1;
    }
}

.animate-pulse-custom {
    animation: pulse-custom 1.5s infinite;
}
</style>
