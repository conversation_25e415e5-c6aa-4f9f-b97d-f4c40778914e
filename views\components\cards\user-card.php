<?php
/**
 * User Card Component
 * Dynamic reusable user card component
 * 
 * @param array $user - User data (id, first_name, last_name, username, email, avatar, role, etc.)
 * @param string $size - Card size: sm, md, lg (default: md)
 * @param bool $showActions - Show action buttons (default: false)
 * @param bool $showRole - Show user role (default: true)
 * @param bool $showEmail - Show user email (default: false)
 * @param bool $showStats - Show user stats (default: false)
 * @param string $layout - Card layout: horizontal, vertical (default: vertical)
 * @param array $actions - Custom action buttons
 * @param string $href - Make card clickable with link
 */

// Extract data from passed parameters
$user = $data['user'] ?? [];
$size = $data['size'] ?? 'md';
$showActions = $data['showActions'] ?? false;
$showRole = $data['showRole'] ?? true;
$showEmail = $data['showEmail'] ?? false;
$showStats = $data['showStats'] ?? false;
$layout = $data['layout'] ?? 'vertical';
$actions = $data['actions'] ?? [];
$href = $data['href'] ?? '';

// User data with defaults
$userId = $user['id'] ?? 0;
$firstName = $user['first_name'] ?? 'Unknown';
$lastName = $user['last_name'] ?? 'User';
$username = $user['username'] ?? 'unknown';
$email = $user['email'] ?? '';
$avatar = $user['avatar'] ?? '';
$role = $user['role'] ?? 'user';
$status = $user['status'] ?? 'active';
$joinDate = $user['created_at'] ?? '';
$lastLogin = $user['last_login_at'] ?? '';

// Stats data
$stats = $user['stats'] ?? [
    'quizzes' => 0,
    'points' => 0,
    'rank' => 0
];

// Size classes
$sizeClasses = [
    'sm' => 'p-3',
    'md' => 'p-4',
    'lg' => 'p-6'
];

// Avatar size classes
$avatarSizes = [
    'sm' => 'w-10 h-10',
    'md' => 'w-12 h-12',
    'lg' => 'w-16 h-16'
];

// Role colors
$roleColors = [
    'admin' => 'bg-red-100 text-red-800',
    'contributor' => 'bg-blue-100 text-blue-800',
    'creator' => 'bg-blue-100 text-blue-800',
    'user' => 'bg-green-100 text-green-800',
    'premium' => 'bg-purple-100 text-purple-800'
];

// Status colors
$statusColors = [
    'active' => 'bg-green-100 text-green-800',
    'inactive' => 'bg-gray-100 text-gray-800',
    'suspended' => 'bg-red-100 text-red-800'
];

// Default avatar
$defaultAvatar = '/public/assets/images/default-avatar.png';
$avatarUrl = $avatar ? '/public/uploads/avatars/' . $avatar : $defaultAvatar;

// Full name
$fullName = trim($firstName . ' ' . $lastName);

// Card classes
$cardClasses = 'bg-white rounded-lg border border-gray-200 shadow-sm hover:shadow-md transition-all duration-200 ' . $sizeClasses[$size];

if ($href) {
    $cardClasses .= ' cursor-pointer hover:border-blue-300';
}
?>

<div class="user-card <?= $cardClasses ?>" 
     <?= $href ? 'onclick="window.location.href=\'' . htmlspecialchars($href) . '\'"' : '' ?>
     data-user-id="<?= $userId ?>">
    
    <?php if ($layout === 'horizontal'): ?>
        <!-- Horizontal Layout -->
        <div class="flex items-center space-x-4">
            <!-- Avatar -->
            <div class="flex-shrink-0">
                <img class="<?= $avatarSizes[$size] ?> rounded-full object-cover border-2 border-gray-200" 
                     src="<?= htmlspecialchars($avatarUrl) ?>" 
                     alt="<?= htmlspecialchars($fullName) ?>"
                     onerror="this.src='<?= $defaultAvatar ?>'">
                
                <!-- Online status indicator -->
                <?php if (isset($user['is_online']) && $user['is_online']): ?>
                <div class="absolute -bottom-1 -right-1 w-3 h-3 bg-green-400 border-2 border-white rounded-full"></div>
                <?php endif; ?>
            </div>
            
            <!-- User Info -->
            <div class="flex-1 min-w-0">
                <div class="flex items-center justify-between">
                    <div>
                        <h3 class="text-sm font-medium text-gray-900 truncate">
                            <?= htmlspecialchars($fullName) ?>
                        </h3>
                        <p class="text-sm text-gray-500 truncate">@<?= htmlspecialchars($username) ?></p>
                        
                        <?php if ($showEmail && $email): ?>
                        <p class="text-xs text-gray-400 truncate"><?= htmlspecialchars($email) ?></p>
                        <?php endif; ?>
                    </div>
                    
                    <!-- Role Badge -->
                    <?php if ($showRole): ?>
                    <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium <?= $roleColors[$role] ?? $roleColors['user'] ?>">
                        <?= ucfirst($role) ?>
                    </span>
                    <?php endif; ?>
                </div>
                
                <!-- Stats -->
                <?php if ($showStats): ?>
                <div class="mt-2 flex space-x-4 text-xs text-gray-500">
                    <span>Quizzes: <?= number_format($stats['quizzes']) ?></span>
                    <span>Points: <?= number_format($stats['points']) ?></span>
                    <?php if ($stats['rank']): ?>
                    <span>Rank: #<?= number_format($stats['rank']) ?></span>
                    <?php endif; ?>
                </div>
                <?php endif; ?>
            </div>
        </div>
        
    <?php else: ?>
        <!-- Vertical Layout -->
        <div class="text-center">
            <!-- Avatar -->
            <div class="relative inline-block mb-3">
                <img class="<?= $avatarSizes[$size] ?> rounded-full object-cover border-2 border-gray-200 mx-auto" 
                     src="<?= htmlspecialchars($avatarUrl) ?>" 
                     alt="<?= htmlspecialchars($fullName) ?>"
                     onerror="this.src='<?= $defaultAvatar ?>'">
                
                <!-- Online status indicator -->
                <?php if (isset($user['is_online']) && $user['is_online']): ?>
                <div class="absolute -bottom-1 -right-1 w-3 h-3 bg-green-400 border-2 border-white rounded-full"></div>
                <?php endif; ?>
            </div>
            
            <!-- User Info -->
            <div>
                <h3 class="text-sm font-medium text-gray-900 mb-1">
                    <?= htmlspecialchars($fullName) ?>
                </h3>
                <p class="text-sm text-gray-500 mb-2">@<?= htmlspecialchars($username) ?></p>
                
                <?php if ($showEmail && $email): ?>
                <p class="text-xs text-gray-400 mb-2"><?= htmlspecialchars($email) ?></p>
                <?php endif; ?>
                
                <!-- Role Badge -->
                <?php if ($showRole): ?>
                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium <?= $roleColors[$role] ?? $roleColors['user'] ?> mb-2">
                    <?= ucfirst($role) ?>
                </span>
                <?php endif; ?>
                
                <!-- Stats -->
                <?php if ($showStats): ?>
                <div class="mt-3 grid grid-cols-3 gap-2 text-xs text-gray-500">
                    <div>
                        <div class="font-medium text-gray-900"><?= number_format($stats['quizzes']) ?></div>
                        <div>Quizzes</div>
                    </div>
                    <div>
                        <div class="font-medium text-gray-900"><?= number_format($stats['points']) ?></div>
                        <div>Points</div>
                    </div>
                    <?php if ($stats['rank']): ?>
                    <div>
                        <div class="font-medium text-gray-900">#<?= number_format($stats['rank']) ?></div>
                        <div>Rank</div>
                    </div>
                    <?php endif; ?>
                </div>
                <?php endif; ?>
            </div>
        </div>
    <?php endif; ?>
    
    <!-- Actions -->
    <?php if ($showActions || !empty($actions)): ?>
    <div class="mt-4 pt-3 border-t border-gray-200">
        <div class="flex justify-center space-x-2">
            <?php if (!empty($actions)): ?>
                <?php foreach ($actions as $action): ?>
                <button type="button" 
                        class="px-3 py-1 text-xs font-medium text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-md transition-colors duration-200"
                        onclick="<?= htmlspecialchars($action['onclick'] ?? '') ?>">
                    <?= htmlspecialchars($action['text'] ?? 'Action') ?>
                </button>
                <?php endforeach; ?>
            <?php else: ?>
                <!-- Default actions -->
                <button type="button" 
                        class="px-3 py-1 text-xs font-medium text-blue-700 bg-blue-100 hover:bg-blue-200 rounded-md transition-colors duration-200"
                        onclick="viewUser(<?= $userId ?>)">
                    View Profile
                </button>
                <button type="button" 
                        class="px-3 py-1 text-xs font-medium text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-md transition-colors duration-200"
                        onclick="messageUser(<?= $userId ?>)">
                    Message
                </button>
            <?php endif; ?>
        </div>
    </div>
    <?php endif; ?>
</div>

<script>
function viewUser(userId) {
    window.location.href = '/user/profile/' + userId;
}

function messageUser(userId) {
    // Implement messaging functionality
    console.log('Message user:', userId);
}
</script>
