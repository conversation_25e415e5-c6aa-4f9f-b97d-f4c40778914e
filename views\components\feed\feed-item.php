<?php
/**
 * Feed Item Component
 * Dynamic reusable feed item component for all types (Quiz, Social, E-commerce, Freelance)
 * 
 * @param array $item - Feed item data
 * @param string $type - Feed type: quiz, social, ecommerce, freelance
 * @param bool $showActions - Show action buttons (default: true)
 * @param bool $showAuthor - Show author info (default: true)
 * @param bool $showTimestamp - Show timestamp (default: true)
 * @param string $size - Item size: sm, md, lg (default: md)
 */

// Extract data from passed parameters
$item = $data['item'] ?? [];
$type = $data['type'] ?? 'social';
$showActions = $data['showActions'] ?? true;
$showAuthor = $data['showAuthor'] ?? true;
$showTimestamp = $data['showTimestamp'] ?? true;
$size = $data['size'] ?? 'md';

// Common item data
$itemId = $item['id'] ?? 0;
$title = $item['title'] ?? '';
$description = $item['description'] ?? '';
$content = $item['content'] ?? '';
$image = $item['image'] ?? '';
$createdAt = $item['created_at'] ?? '';
$updatedAt = $item['updated_at'] ?? '';

// Author data
$author = $item['author'] ?? [];
$authorName = $author['first_name'] ?? '' . ' ' . ($author['last_name'] ?? '');
$authorUsername = $author['username'] ?? 'unknown';
$authorAvatar = $author['avatar'] ?? '';
$authorRole = $author['role'] ?? 'user';

// Type-specific data
$typeData = $item['type_data'] ?? [];

// Size classes
$sizeClasses = [
    'sm' => 'p-3',
    'md' => 'p-4',
    'lg' => 'p-6'
];

// Type colors and icons
$typeConfig = [
    'quiz' => [
        'color' => 'blue',
        'icon' => '<svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-8-3a1 1 0 00-.867.5 1 1 0 11-1.731-1A3 3 0 0113 8a3.001 3.001 0 01-2 2.83V11a1 1 0 11-2 0v-1a1 1 0 011-1 1 1 0 100-2zm0 8a1 1 0 100-2 1 1 0 000 2z" clip-rule="evenodd"></path></svg>',
        'label' => 'Quiz'
    ],
    'social' => [
        'color' => 'green',
        'icon' => '<svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M18 13V5a2 2 0 00-2-2H4a2 2 0 00-2 2v8a2 2 0 002 2h3l3 3 3-3h3a2 2 0 002-2zM5 7a1 1 0 011-1h8a1 1 0 110 2H6a1 1 0 01-1-1zm1 3a1 1 0 100 2h3a1 1 0 100-2H6z" clip-rule="evenodd"></path></svg>',
        'label' => 'Post'
    ],
    'ecommerce' => [
        'color' => 'purple',
        'icon' => '<svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M10 2L3 7v11a2 2 0 002 2h10a2 2 0 002-2V7l-7-5zM6 9a1 1 0 112 0 1 1 0 01-2 0zm6 0a1 1 0 112 0 1 1 0 01-2 0z" clip-rule="evenodd"></path></svg>',
        'label' => 'Product'
    ],
    'freelance' => [
        'color' => 'orange',
        'icon' => '<svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M6 6V5a3 3 0 013-3h2a3 3 0 013 3v1h2a2 2 0 012 2v3.57A22.952 22.952 0 0110 13a22.95 22.95 0 01-8-1.43V8a2 2 0 012-2h2zm2-1a1 1 0 011-1h2a1 1 0 011 1v1H8V5zm1 5a1 1 0 011-1h.01a1 1 0 110 2H10a1 1 0 01-1-1z" clip-rule="evenodd"></path></svg>',
        'label' => 'Job'
    ]
];

$config = $typeConfig[$type] ?? $typeConfig['social'];
$colorClass = 'text-' . $config['color'] . '-600';

// Default avatar
$defaultAvatar = '/public/assets/images/default-avatar.png';
$authorAvatarUrl = $authorAvatar ? '/public/uploads/avatars/' . $authorAvatar : $defaultAvatar;

// Time ago function
function timeAgo($datetime) {
    $time = time() - strtotime($datetime);
    
    if ($time < 60) return 'just now';
    if ($time < 3600) return floor($time/60) . 'm ago';
    if ($time < 86400) return floor($time/3600) . 'h ago';
    if ($time < 2592000) return floor($time/86400) . 'd ago';
    
    return date('M j, Y', strtotime($datetime));
}
?>

<div class="feed-item bg-white rounded-lg border border-gray-200 shadow-sm hover:shadow-md transition-all duration-200 <?= $sizeClasses[$size] ?>" 
     data-item-id="<?= $itemId ?>" 
     data-type="<?= $type ?>">
    
    <!-- Header -->
    <div class="flex items-start justify-between mb-3">
        <div class="flex items-center space-x-3">
            <!-- Type Icon -->
            <div class="flex-shrink-0 <?= $colorClass ?>">
                <?= $config['icon'] ?>
            </div>
            
            <!-- Author Info -->
            <?php if ($showAuthor): ?>
            <div class="flex items-center space-x-2">
                <img class="w-8 h-8 rounded-full object-cover border border-gray-200" 
                     src="<?= htmlspecialchars($authorAvatarUrl) ?>" 
                     alt="<?= htmlspecialchars($authorName) ?>"
                     onerror="this.src='<?= $defaultAvatar ?>'">
                
                <div>
                    <h4 class="text-sm font-medium text-gray-900">
                        <?= htmlspecialchars(trim($authorName)) ?>
                    </h4>
                    <p class="text-xs text-gray-500">@<?= htmlspecialchars($authorUsername) ?></p>
                </div>
            </div>
            <?php endif; ?>
        </div>
        
        <!-- Type Badge & Timestamp -->
        <div class="flex items-center space-x-2">
            <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-<?= $config['color'] ?>-100 text-<?= $config['color'] ?>-800">
                <?= $config['label'] ?>
            </span>
            
            <?php if ($showTimestamp && $createdAt): ?>
            <span class="text-xs text-gray-500">
                <?= timeAgo($createdAt) ?>
            </span>
            <?php endif; ?>
        </div>
    </div>
    
    <!-- Content -->
    <div class="mb-3">
        <?php if ($title): ?>
        <h3 class="text-lg font-semibold text-gray-900 mb-2 line-clamp-2">
            <?= htmlspecialchars($title) ?>
        </h3>
        <?php endif; ?>
        
        <?php if ($description): ?>
        <p class="text-gray-700 text-sm mb-2 line-clamp-3">
            <?= htmlspecialchars($description) ?>
        </p>
        <?php endif; ?>
        
        <?php if ($content && !$description): ?>
        <p class="text-gray-700 text-sm mb-2 line-clamp-3">
            <?= htmlspecialchars($content) ?>
        </p>
        <?php endif; ?>
    </div>
    
    <!-- Image -->
    <?php if ($image): ?>
    <div class="mb-3">
        <img class="w-full h-48 object-cover rounded-lg border border-gray-200" 
             src="<?= htmlspecialchars($image) ?>" 
             alt="<?= htmlspecialchars($title) ?>"
             onclick="openImageModal('<?= htmlspecialchars($image) ?>')">
    </div>
    <?php endif; ?>
    
    <!-- Type-specific Content -->
    <?php if ($type === 'quiz' && !empty($typeData)): ?>
        <!-- Quiz specific content -->
        <div class="bg-blue-50 rounded-lg p-3 mb-3">
            <div class="flex items-center justify-between text-sm">
                <span class="text-blue-700">
                    <strong><?= $typeData['questions_count'] ?? 0 ?></strong> Questions
                </span>
                <span class="text-blue-700">
                    <strong><?= $typeData['time_limit'] ?? 0 ?></strong> Minutes
                </span>
                <span class="text-blue-700">
                    Difficulty: <strong><?= ucfirst($typeData['difficulty'] ?? 'medium') ?></strong>
                </span>
            </div>
        </div>
        
    <?php elseif ($type === 'ecommerce' && !empty($typeData)): ?>
        <!-- Product specific content -->
        <div class="bg-purple-50 rounded-lg p-3 mb-3">
            <div class="flex items-center justify-between">
                <div class="text-lg font-bold text-purple-900">
                    $<?= number_format($typeData['price'] ?? 0, 2) ?>
                </div>
                <div class="text-sm text-purple-700">
                    <?= $typeData['stock'] ?? 0 ?> in stock
                </div>
            </div>
        </div>
        
    <?php elseif ($type === 'freelance' && !empty($typeData)): ?>
        <!-- Job specific content -->
        <div class="bg-orange-50 rounded-lg p-3 mb-3">
            <div class="flex items-center justify-between text-sm">
                <span class="text-orange-700">
                    Budget: <strong>$<?= number_format($typeData['budget'] ?? 0) ?></strong>
                </span>
                <span class="text-orange-700">
                    Deadline: <strong><?= $typeData['deadline'] ?? 'Not specified' ?></strong>
                </span>
            </div>
        </div>
    <?php endif; ?>
    
    <!-- Actions -->
    <?php if ($showActions): ?>
    <div class="flex items-center justify-between pt-3 border-t border-gray-200">
        <div class="flex items-center space-x-4">
            <!-- Like/Heart -->
            <button type="button" 
                    class="flex items-center space-x-1 text-gray-500 hover:text-red-500 transition-colors duration-200"
                    onclick="toggleLike(<?= $itemId ?>, '<?= $type ?>')">
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"></path>
                </svg>
                <span class="text-sm"><?= $item['likes_count'] ?? 0 ?></span>
            </button>
            
            <!-- Comment -->
            <button type="button" 
                    class="flex items-center space-x-1 text-gray-500 hover:text-blue-500 transition-colors duration-200"
                    onclick="showComments(<?= $itemId ?>, '<?= $type ?>')">
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path>
                </svg>
                <span class="text-sm"><?= $item['comments_count'] ?? 0 ?></span>
            </button>
            
            <!-- Share -->
            <button type="button" 
                    class="flex items-center space-x-1 text-gray-500 hover:text-green-500 transition-colors duration-200"
                    onclick="shareItem(<?= $itemId ?>, '<?= $type ?>')">
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.367 2.684 3 3 0 00-5.367-2.684z"></path>
                </svg>
                <span class="text-sm">Share</span>
            </button>
        </div>
        
        <!-- Primary Action -->
        <div>
            <?php if ($type === 'quiz'): ?>
                <button type="button" 
                        class="px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-lg hover:bg-blue-700 transition-colors duration-200"
                        onclick="startQuiz(<?= $itemId ?>)">
                    Start Quiz
                </button>
            <?php elseif ($type === 'ecommerce'): ?>
                <button type="button" 
                        class="px-4 py-2 bg-purple-600 text-white text-sm font-medium rounded-lg hover:bg-purple-700 transition-colors duration-200"
                        onclick="viewProduct(<?= $itemId ?>)">
                    View Product
                </button>
            <?php elseif ($type === 'freelance'): ?>
                <button type="button" 
                        class="px-4 py-2 bg-orange-600 text-white text-sm font-medium rounded-lg hover:bg-orange-700 transition-colors duration-200"
                        onclick="viewJob(<?= $itemId ?>)">
                    View Job
                </button>
            <?php else: ?>
                <button type="button" 
                        class="px-4 py-2 bg-green-600 text-white text-sm font-medium rounded-lg hover:bg-green-700 transition-colors duration-200"
                        onclick="viewPost(<?= $itemId ?>)">
                    View Post
                </button>
            <?php endif; ?>
        </div>
    </div>
    <?php endif; ?>
</div>

<script>
function toggleLike(itemId, type) {
    // Implement like functionality
    console.log('Toggle like:', itemId, type);
}

function showComments(itemId, type) {
    // Implement comments functionality
    console.log('Show comments:', itemId, type);
}

function shareItem(itemId, type) {
    // Implement share functionality
    console.log('Share item:', itemId, type);
}

function startQuiz(quizId) {
    window.location.href = '/quiz/start/' + quizId;
}

function viewProduct(productId) {
    window.location.href = '/product/' + productId;
}

function viewJob(jobId) {
    window.location.href = '/job/' + jobId;
}

function viewPost(postId) {
    window.location.href = '/post/' + postId;
}

function openImageModal(imageUrl) {
    // Implement image modal functionality
    console.log('Open image:', imageUrl);
}
</script>
