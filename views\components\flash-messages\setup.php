<?php
/**
 * 🚀 ULTIMATE FLASH MESSAGES - SETUP & DEMO
 * Global Settings Manager & Live Demo Interface
 * 
 * This file allows you to:
 * 1. Test all flash message functions
 * 2. Customize global settings
 * 3. Save settings directly to ultimate-flash.php
 * 4. See live preview of changes
 */

// Start session for flash messages
session_start();

// Include the ultimate flash system
require_once __DIR__ . '/ultimate-flash.php';

// Handle settings save
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['save_settings'])) {
    $success = saveGlobalSettings($_POST);
    if ($success) {
        flash_success('Settings saved successfully! Changes applied to ultimate-flash.php');
    } else {
        flash_error('Failed to save settings. Please check file permissions.');
    }
    header('Location: ' . $_SERVER['PHP_SELF']);
    exit;
}

// Handle demo actions
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['demo_action'])) {
    $action = $_POST['demo_action'];
    
    switch ($action) {
        // Gaming Functions
        case 'quiz_victory':
            UltimateFlash::quizVictory(8, 10, 80);
            break;
        case 'perfect_score':
            UltimateFlash::perfectScore('Mathematics');
            break;
        case 'daily_coins':
            UltimateFlash::dailyCoins(150);
            break;
        case 'streak':
            UltimateFlash::streakAchievement(7, 'login');
            break;
        case 'badge':
            UltimateFlash::badgeUnlocked('Quiz Master', 'Complete 10 quizzes');
            break;
        case 'rank_up':
            UltimateFlash::rankPromotion('Gold Member', 'Silver Member');
            break;
            
        // Social Functions
        case 'welcome_back':
            UltimateFlash::welcomeBack('John Doe', '2 hours ago');
            break;
        case 'new_follower':
            UltimateFlash::newFollower('Jane Smith');
            break;
        case 'post_liked':
            UltimateFlash::postLiked('Mike Johnson', 'My awesome post');
            break;
        case 'viral_post':
            UltimateFlash::viralPost('Amazing tutorial!', 1500, 250);
            break;
            
        // E-commerce Functions
        case 'add_to_cart':
            UltimateFlash::itemAddedToCart('Wireless Headphones', '$99.99');
            break;
        case 'order_confirmed':
            UltimateFlash::orderConfirmed('#ORD-12345', '$299.99', '3 items');
            break;
        case 'order_shipped':
            UltimateFlash::orderShipped('#ORD-12345', 'TRK-789456');
            break;
        case 'flash_sale':
            UltimateFlash::flashSale('50% OFF', '2 hours left');
            break;
            
        // Freelance Functions
        case 'job_awarded':
            UltimateFlash::jobAwarded('Website Development', '$750', 'TechCorp');
            break;
        case 'payment_received':
            UltimateFlash::paymentReceived('$750', 'TechCorp', 'Website Project');
            break;
            
        // Learning Functions
        case 'course_completed':
            UltimateFlash::courseCompleted('PHP Advanced', 'A+', true);
            break;
        case 'skill_mastered':
            UltimateFlash::skillMastered('React.js', 'Expert');
            break;

        // Advanced Engagement Functions
        case 'daily_coin_summary':
            UltimateFlash::dailyCoinSummary(250, [
                'Quiz Completed' => 50,
                'Daily Login' => 25,
                'Social Interaction' => 75,
                'Course Progress' => 100
            ]);
            break;
        case 'gaming_session':
            UltimateFlash::gamingSessionSummary('45 minutes', 8, 180, ['Speed Master', 'Perfect Score']);
            break;
        case 'progress_milestone':
            UltimateFlash::progressMilestone('Learning Progress', 75, 100, 75);
            break;
        case 'weekly_summary':
            UltimateFlash::weeklySummary([
                'Coins Earned' => ['Total' => 1250, 'Daily Average' => 178],
                'Quizzes' => ['Completed' => 15, 'Perfect Scores' => 8],
                'Social' => ['Posts' => 12, 'Likes Received' => 45]
            ]);
            break;
        case 'smart_motivation':
            UltimateFlash::smartMotivation('high', 'morning');
            break;
        case 'special_event':
            UltimateFlash::specialEvent('Double Coin Weekend', 'Earn 2x coins on all activities!', '48 hours', ['Double Coins', 'Exclusive Badge', 'Bonus XP']);
            break;
        case 'leaderboard_update':
            UltimateFlash::leaderboardUpdate(3, 'Quiz Masters', 2450, '+2 positions');
            break;
        case 'customization_unlocked':
            UltimateFlash::customizationUnlocked('Golden Theme', 'Themes', 'Reach 1000 coins');
            break;
        case 'feature_discovery':
            UltimateFlash::featureDiscovery('Smart Study Planner', 'AI-powered study recommendations based on your progress', ['Personalized Learning', 'Better Results', 'Time Optimization']);
            break;

        // CRUD Operations
        case 'data_added':
            UltimateFlash::dataAdded('User', 'John Doe');
            break;
        case 'data_edited':
            UltimateFlash::dataEdited('Profile', 'User Settings');
            break;
        case 'data_updated':
            UltimateFlash::dataUpdated('Post', 'Blog Article');
            break;
        case 'data_created':
            UltimateFlash::dataCreated('Project', 'New Website');
            break;
        case 'data_imported':
            UltimateFlash::dataImported('Users', 150);
            break;
        case 'data_exported':
            UltimateFlash::dataExported('Reports', 'PDF');
            break;
        case 'data_restored':
            UltimateFlash::dataRestored('Backup', 'Database');
            break;
        case 'data_published':
            UltimateFlash::dataPublished('Article', 'How to Code');
            break;
        case 'registration_success':
            UltimateFlash::registrationSuccess();
            break;
        case 'logout_success':
            UltimateFlash::logoutSuccess('John Doe');
            break;

        // System Functions
        case 'login_success':
            UltimateFlash::loginSuccess('John Doe', '2 hours ago');
            break;
        case 'email_sent':
            UltimateFlash::emailSent('<EMAIL>', 'Welcome Message');
            break;
        case 'file_uploaded':
            UltimateFlash::fileUploaded('document.pdf', '2.5MB');
            break;
        case 'file_downloaded':
            UltimateFlash::fileDownloaded('report.xlsx', '1.2MB');
            break;
        case 'data_synced':
            UltimateFlash::dataSynced('User profiles', '2 minutes ago');
            break;
        case 'account_locked':
            UltimateFlash::accountLocked('Too many failed attempts', '30 minutes');
            break;
        case 'account_unlocked':
            UltimateFlash::accountUnlocked('John Doe');
            break;
        case 'password_changed':
            UltimateFlash::passwordChanged('John Doe');
            break;
        case 'backup_created':
            UltimateFlash::backupCreated('backup_2024.sql', '15MB');
            break;
        case 'system_updated':
            UltimateFlash::systemUpdated('v2.1.0', 'New dashboard, Bug fixes');
            break;
        case 'maintenance_mode':
            UltimateFlash::maintenanceMode('2 hours', 'Server upgrade');
            break;
        case 'system_online':
            UltimateFlash::systemOnline('45 minutes');
            break;
        case 'performance_alert':
            UltimateFlash::performanceAlert('CPU Usage', '85%', '80%');
            break;
        case 'realtime_notification':
            UltimateFlash::realtimeNotification('New Message', 'You have a new message from admin', 'Admin');
            break;

        // Advanced UI Functions
        case 'progress':
            UltimateFlash::progress('Processing data...', 'Please wait', 65);
            break;
        case 'loading':
            UltimateFlash::loading('Loading content...');
            break;
        case 'prompt':
            UltimateFlash::prompt('Enter your name:', 'User Input', 'John Doe');
            break;
        case 'date_picker':
            UltimateFlash::datePicker('Select your birthday:', 'Date Selection');
            break;
        case 'list_selector':
            UltimateFlash::listSelector('Choose your country:', 'Country Selection', ['USA', 'Canada', 'UK', 'Australia']);
            break;
        case 'color_picker':
            UltimateFlash::colorPicker('Pick your favorite color:', 'Color Selection', '#3b82f6');
            break;
        case 'file_selector':
            UltimateFlash::fileSelector('Upload your resume:', 'File Upload');
            break;
        case 'image_preview':
            UltimateFlash::imagePreview('https://via.placeholder.com/400x300', 'Sample Image');
            break;
        case 'video_player':
            UltimateFlash::videoPlayer('https://sample-videos.com/zip/10/mp4/SampleVideo_1280x720_1mb.mp4', 'Sample Video');
            break;
        case 'chart':
            UltimateFlash::chart(['Jan' => 100, 'Feb' => 150, 'Mar' => 200], 'Monthly Sales', 'bar');
            break;
        case 'calendar':
            UltimateFlash::calendar([
                ['title' => 'Meeting', 'date' => '2024-01-15'],
                ['title' => 'Deadline', 'date' => '2024-01-20']
            ], 'Event Calendar');
            break;

        // More Gaming Functions
        case 'quiz_completed':
            UltimateFlash::quizCompleted('8/10');
            break;
        case 'correct_answer':
            UltimateFlash::correctAnswer();
            break;
        case 'wrong_answer':
            UltimateFlash::wrongAnswer();
            break;
        case 'level_up':
            UltimateFlash::levelUp('Level 5');
            break;
        case 'points_earned':
            UltimateFlash::pointsEarned('50');
            break;
        case 'bonus_coins':
            UltimateFlash::bonusCoins(25, 'daily bonus');
            break;
        case 'first_time':
            UltimateFlash::firstTime('completing a quiz');
            break;
        case 'competition_win':
            UltimateFlash::competitionWin('Math Challenge', 1);
            break;
        case 'game_over':
            UltimateFlash::gameOver(1250, 980);
            break;
        case 'daily_reward':
            UltimateFlash::dailyReward('100 coins + Badge', 7);
            break;
        case 'challenge_completed':
            UltimateFlash::challengeCompleted('Complete 5 quizzes', '200 coins');
            break;

        // More Social Functions
        case 'mentioned':
            UltimateFlash::mentioned('Sarah Wilson', 'a comment');
            break;
        case 'post_published':
            UltimateFlash::postPublished('How to Learn PHP');
            break;

        // More E-commerce Functions
        case 'payment_processing':
            UltimateFlash::paymentProcessing('$299.99');
            break;
        case 'refund_processed':
            UltimateFlash::refundProcessed('$99.99', '#ORD-12345');
            break;
        case 'review_submitted':
            UltimateFlash::reviewSubmitted('Wireless Headphones', '5 stars');
            break;

        // More Freelance Functions
        case 'new_job_posted':
            UltimateFlash::newJobPosted('Mobile App Development', '$1000-2000', 'TechStartup');
            break;
        case 'proposal_submitted':
            UltimateFlash::proposalSubmitted('Website Redesign', '$750');
            break;
        case 'client_review':
            UltimateFlash::clientReview(5, 'TechCorp', 'Excellent work, delivered on time!');
            break;

        // More Learning Functions
        case 'lesson_completed':
            UltimateFlash::lessonCompleted('PHP Basics', '75% complete');
            break;
        case 'certificate_earned':
            UltimateFlash::certificateEarned('Web Development Certificate', 'CodeAcademy');
            break;

        // More CRUD Operations
        case 'data_duplicated':
            UltimateFlash::dataDuplicated('Template', 'Email Template');
            break;
        case 'data_archived':
            UltimateFlash::dataArchived('Posts', 5);
            break;
        case 'data_unarchived':
            UltimateFlash::dataUnarchived('Posts', 3);
            break;
        case 'data_drafted':
            UltimateFlash::dataDrafted('Article', 'Draft Article');
            break;
        case 'data_found':
            UltimateFlash::dataFound('Users', 25);
            break;
        case 'data_not_found':
            UltimateFlash::dataNotFound('Results');
            break;
        case 'data_synchronized':
            UltimateFlash::dataSynchronized('Database', 150);
            break;

        // Basic Functions
        case 'success':
            flash_success('Operation completed successfully!');
            break;
        case 'error':
            flash_error('Something went wrong. Please try again.');
            break;
        case 'warning':
            flash_warning('Please check your input before proceeding.');
            break;
        case 'info':
            flash_info('Here is some important information.');
            break;
        case 'confirm':
            UltimateFlash::confirm('Are you sure you want to delete this item?', 'Confirm Delete');
            break;
        case 'alert':
            UltimateFlash::alert('This is an important alert message!', 'Alert');
            break;
        case 'modal':
            UltimateFlash::modal('This is a modal dialog with custom content.', 'Modal Dialog');
            break;
    }

    // Redirect to prevent form resubmission
    header('Location: ' . $_SERVER['PHP_SELF']);
    exit;
    
    header('Location: ' . $_SERVER['PHP_SELF']);
    exit;
}

/**
 * Save global settings to ultimate-flash.php file
 */
function saveGlobalSettings($settings) {
    $filePath = __DIR__ . '/ultimate-flash.php';
    
    if (!is_writable($filePath)) {
        return false;
    }
    
    $content = file_get_contents($filePath);
    
    // Update theme
    if (isset($settings['theme'])) {
        $content = preg_replace(
            '/public static \$theme = \'[^\']*\';/',
            "public static \$theme = '{$settings['theme']}';",
            $content
        );
    }
    
    // Update position
    if (isset($settings['position'])) {
        $content = preg_replace(
            '/public static \$position = \'[^\']*\';/',
            "public static \$position = '{$settings['position']}';",
            $content
        );
    }
    
    // Update animation style
    if (isset($settings['animation'])) {
        $content = preg_replace(
            '/public static \$animationStyle = \'[^\']*\';/',
            "public static \$animationStyle = '{$settings['animation']}';",
            $content
        );
    }
    
    // Update duration
    if (isset($settings['duration'])) {
        $content = preg_replace(
            '/public static \$duration = \d+;/',
            "public static \$duration = {$settings['duration']};",
            $content
        );
    }
    
    // Update auto hide
    if (isset($settings['auto_hide'])) {
        $autoHide = $settings['auto_hide'] === '1' ? 'true' : 'false';
        $content = preg_replace(
            '/public static \$autoHide = (true|false);/',
            "public static \$autoHide = {$autoHide};",
            $content
        );
    }
    
    // Update show progress
    if (isset($settings['show_progress'])) {
        $showProgress = $settings['show_progress'] === '1' ? 'true' : 'false';
        $content = preg_replace(
            '/public static \$showProgress = (true|false);/',
            "public static \$showProgress = {$showProgress};",
            $content
        );
    }
    
    // Update click to dismiss
    if (isset($settings['click_dismiss'])) {
        $clickDismiss = $settings['click_dismiss'] === '1' ? 'true' : 'false';
        $content = preg_replace(
            '/public static \$clickToDismiss = (true|false);/',
            "public static \$clickToDismiss = {$clickDismiss};",
            $content
        );
    }
    
    // Update enable sounds
    $enableSounds = isset($settings['enable_sounds']) && $settings['enable_sounds'] === '1' ? 'true' : 'false';
    $content = preg_replace(
        '/public static \$enableSounds = (true|false);/',
        "public static \$enableSounds = {$enableSounds};",
        $content
    );

    // Update auto hide
    $autoHide = isset($settings['auto_hide']) && $settings['auto_hide'] === '1' ? 'true' : 'false';
    $content = preg_replace(
        '/public static \$autoHide = (true|false);/',
        "public static \$autoHide = {$autoHide};",
        $content
    );

    // Update show progress
    $showProgress = isset($settings['show_progress']) && $settings['show_progress'] === '1' ? 'true' : 'false';
    $content = preg_replace(
        '/public static \$showProgress = (true|false);/',
        "public static \$showProgress = {$showProgress};",
        $content
    );

    // Update click to dismiss
    $clickDismiss = isset($settings['click_dismiss']) && $settings['click_dismiss'] === '1' ? 'true' : 'false';
    $content = preg_replace(
        '/public static \$clickToDismiss = (true|false);/',
        "public static \$clickToDismiss = {$clickDismiss};",
        $content
    );

    return file_put_contents($filePath, $content) !== false;
}

/**
 * Get current settings from ultimate-flash.php
 */
function getCurrentSettings() {
    return [
        'theme' => UltimateFlash::$theme,
        'position' => UltimateFlash::$position,
        'animation' => UltimateFlash::$animationStyle,
        'duration' => UltimateFlash::$duration,
        'auto_hide' => UltimateFlash::$autoHide,
        'show_progress' => UltimateFlash::$showProgress,
        'click_dismiss' => UltimateFlash::$clickToDismiss,
        'enable_sounds' => UltimateFlash::$enableSounds,
    ];
}

$currentSettings = getCurrentSettings();
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Ultimate Flash Messages - Setup & Demo</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: system-ui, -apple-system, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(20px);
            border-radius: 20px;
            padding: 30px;
            color: white;
        }
        
        h1 {
            text-align: center;
            font-size: 2.5rem;
            margin-bottom: 10px;
            background: linear-gradient(45deg, #fff, #f0f0f0);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }
        
        .subtitle {
            text-align: center;
            margin-bottom: 30px;
            opacity: 0.9;
            font-size: 1.1rem;
        }
        
        .main-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            margin-bottom: 30px;
        }
        
        .panel {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 25px;
        }
        
        .panel h3 {
            margin-bottom: 20px;
            font-size: 1.3rem;
            text-align: center;
        }
        
        .settings-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
            margin-bottom: 20px;
        }
        
        .setting-group {
            display: flex;
            flex-direction: column;
            gap: 8px;
        }
        
        .setting-group label {
            font-weight: 600;
            font-size: 14px;
        }
        
        .setting-group select,
        .setting-group input[type="range"],
        .setting-group input[type="number"] {
            padding: 8px 12px;
            border: none;
            border-radius: 8px;
            background: rgba(255, 255, 255, 0.2);
            color: white;
            font-size: 14px;
        }
        
        .setting-group select option {
            background: #333;
            color: white;
        }
        
        .checkbox-group {
            display: flex;
            align-items: center;
            gap: 10px;
            margin: 10px 0;
        }
        
        .checkbox-group input[type="checkbox"] {
            width: 18px;
            height: 18px;
        }
        
        .save-btn {
            width: 100%;
            padding: 12px;
            background: linear-gradient(45deg, #10b981, #059669);
            color: white;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: transform 0.2s;
        }
        
        .save-btn:hover {
            transform: translateY(-2px);
        }
        
        .demo-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
        }
        
        .demo-section {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 10px;
            padding: 15px;
        }
        
        .demo-section h4 {
            margin-bottom: 10px;
            font-size: 1rem;
            text-align: center;
        }
        
        .demo-btn {
            width: 100%;
            padding: 8px 12px;
            margin: 3px 0;
            border: none;
            border-radius: 6px;
            font-size: 13px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s;
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
        }
        
        .demo-btn:hover {
            transform: translateY(-1px);
            box-shadow: 0 3px 10px rgba(0, 0, 0, 0.3);
        }
        
        .demo-btn.success { background: linear-gradient(45deg, #10b981, #059669); }
        .demo-btn.error { background: linear-gradient(45deg, #ef4444, #dc2626); }
        .demo-btn.warning { background: linear-gradient(45deg, #f59e0b, #d97706); }
        .demo-btn.info { background: linear-gradient(45deg, #3b82f6, #2563eb); }
        .demo-btn.achievement { background: linear-gradient(45deg, #8b5cf6, #7c3aed); }
        
        .stats {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 20px;
            text-align: center;
            grid-column: 1 / -1;
        }
        
        @media (max-width: 768px) {
            .main-grid {
                grid-template-columns: 1fr;
            }
            
            .settings-grid {
                grid-template-columns: 1fr;
            }
            
            .demo-grid {
                grid-template-columns: 1fr;
            }
        }

        /* Analytics Dashboard */
        .analytics-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }

        .analytics-card {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 12px;
            padding: 20px;
            text-align: center;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .analytics-card h4 {
            margin: 0 0 10px 0;
            color: #e2e8f0;
            font-size: 14px;
        }

        .analytics-number {
            font-size: 32px;
            font-weight: bold;
            color: #10b981;
        }

        .analytics-text {
            font-size: 18px;
            font-weight: 600;
            color: #3b82f6;
        }

        .events-chart {
            margin: 15px 0;
        }

        .event-bar {
            display: flex;
            align-items: center;
            gap: 15px;
            margin: 10px 0;
        }

        .event-label {
            min-width: 80px;
            color: #e2e8f0;
            font-size: 14px;
        }

        .event-progress {
            flex: 1;
            height: 20px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
            overflow: hidden;
        }

        .event-fill {
            height: 100%;
            background: linear-gradient(45deg, #10b981, #34d399);
            transition: width 0.3s ease;
        }

        .event-count {
            min-width: 30px;
            color: #10b981;
            font-weight: 600;
        }

        .recent-events {
            max-height: 200px;
            overflow-y: auto;
        }

        .event-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 12px;
            margin: 5px 0;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 6px;
            font-size: 14px;
        }

        .event-type {
            color: #3b82f6;
            font-weight: 600;
        }

        .event-time {
            color: #10b981;
        }

        .event-page {
            color: #e2e8f0;
            opacity: 0.8;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 Ultimate Flash Messages</h1>
        <p class="subtitle">Setup & Demo Interface - Global Settings Manager</p>

        <div class="main-grid">
            <!-- Settings Panel -->
            <div class="panel">
                <h3>⚙️ Global Settings</h3>
                <form method="post">
                    <input type="hidden" name="save_settings" value="1">

                    <div class="settings-grid">
                        <div class="setting-group">
                            <label>🎨 Theme:</label>
                            <select name="theme">
                                <option value="modern" <?= $currentSettings['theme'] === 'modern' ? 'selected' : '' ?>>Modern</option>
                                <option value="glass" <?= $currentSettings['theme'] === 'glass' ? 'selected' : '' ?>>Glass</option>
                                <option value="neon" <?= $currentSettings['theme'] === 'neon' ? 'selected' : '' ?>>Neon</option>
                                <option value="minimal" <?= $currentSettings['theme'] === 'minimal' ? 'selected' : '' ?>>Minimal</option>
                                <option value="dark" <?= $currentSettings['theme'] === 'dark' ? 'selected' : '' ?>>Dark</option>
                                <option value="light" <?= $currentSettings['theme'] === 'light' ? 'selected' : '' ?>>Light</option>
                                <option value="corporate" <?= $currentSettings['theme'] === 'corporate' ? 'selected' : '' ?>>🏢 Corporate</option>
                                <option value="gaming" <?= $currentSettings['theme'] === 'gaming' ? 'selected' : '' ?>>🎮 Gaming</option>
                                <option value="ecommerce" <?= $currentSettings['theme'] === 'ecommerce' ? 'selected' : '' ?>>🛒 E-commerce</option>
                                <option value="educational" <?= $currentSettings['theme'] === 'educational' ? 'selected' : '' ?>>📚 Educational</option>
                                <option value="medical" <?= $currentSettings['theme'] === 'medical' ? 'selected' : '' ?>>🏥 Medical</option>
                            </select>
                        </div>

                        <div class="setting-group">
                            <label>📍 Position:</label>
                            <select name="position">
                                <option value="top-right" <?= $currentSettings['position'] === 'top-right' ? 'selected' : '' ?>>Top Right</option>
                                <option value="top-center" <?= $currentSettings['position'] === 'top-center' ? 'selected' : '' ?>>Top Center</option>
                                <option value="top-left" <?= $currentSettings['position'] === 'top-left' ? 'selected' : '' ?>>Top Left</option>
                                <option value="bottom-right" <?= $currentSettings['position'] === 'bottom-right' ? 'selected' : '' ?>>Bottom Right</option>
                                <option value="bottom-center" <?= $currentSettings['position'] === 'bottom-center' ? 'selected' : '' ?>>Bottom Center</option>
                                <option value="bottom-left" <?= $currentSettings['position'] === 'bottom-left' ? 'selected' : '' ?>>Bottom Left</option>
                                <option value="center" <?= $currentSettings['position'] === 'center' ? 'selected' : '' ?>>Center</option>
                            </select>
                        </div>

                        <div class="setting-group">
                            <label>🎬 Animation:</label>
                            <select name="animation">
                                <option value="slide" <?= $currentSettings['animation'] === 'slide' ? 'selected' : '' ?>>Slide</option>
                                <option value="fade" <?= $currentSettings['animation'] === 'fade' ? 'selected' : '' ?>>Fade</option>
                                <option value="bounce" <?= $currentSettings['animation'] === 'bounce' ? 'selected' : '' ?>>Bounce</option>
                                <option value="zoom" <?= $currentSettings['animation'] === 'zoom' ? 'selected' : '' ?>>Zoom</option>
                                <option value="flip" <?= $currentSettings['animation'] === 'flip' ? 'selected' : '' ?>>Flip</option>
                            </select>
                        </div>

                        <div class="setting-group">
                            <label>⏱️ Duration (ms):</label>
                            <input type="number" name="duration" value="<?= $currentSettings['duration'] ?>" min="1000" max="10000" step="500">
                        </div>
                    </div>

                    <div class="checkbox-group">
                        <input type="checkbox" name="auto_hide" value="1" <?= $currentSettings['auto_hide'] ? 'checked' : '' ?>>
                        <label>🔄 Auto Hide Messages</label>
                    </div>

                    <div class="checkbox-group">
                        <input type="checkbox" name="show_progress" value="1" <?= $currentSettings['show_progress'] ? 'checked' : '' ?>>
                        <label>📊 Show Progress Bar</label>
                    </div>

                    <div class="checkbox-group">
                        <input type="checkbox" name="click_dismiss" value="1" <?= $currentSettings['click_dismiss'] ? 'checked' : '' ?>>
                        <label>👆 Click to Dismiss</label>
                    </div>

                    <div class="checkbox-group">
                        <input type="checkbox" name="enable_sounds" value="1" <?= $currentSettings['enable_sounds'] ? 'checked' : '' ?>>
                        <label>🔊 Enable Sound Effects</label>
                    </div>

                    <div style="margin: 15px 0; padding: 10px; background: rgba(255,255,255,0.1); border-radius: 8px;">
                        <h5 style="margin-bottom: 8px;">🎵 Sound Settings:</h5>
                        <p style="font-size: 12px; line-height: 1.4;">
                            <strong>Success:</strong> Pleasant chime (C5-E5-G5)<br>
                            <strong>Error:</strong> Alert tone (A3-F#3-E3)<br>
                            <strong>Achievement:</strong> Victory fanfare (C5-E5-G5-C6)<br>
                            <strong>Info/Warning:</strong> Notification beep<br>
                            <em>Sounds play automatically when messages appear</em>
                        </p>
                    </div>

                    <button type="submit" class="save-btn">💾 Save Global Settings</button>
                </form>
            </div>

            <!-- Current Status -->
            <div class="panel">
                <h3>📊 Current Status</h3>
                <div style="text-align: left; line-height: 1.8;">
                    <p><strong>🎨 Theme:</strong> <?= ucfirst($currentSettings['theme']) ?></p>
                    <p><strong>📍 Position:</strong> <?= ucfirst(str_replace('-', ' ', $currentSettings['position'])) ?></p>
                    <p><strong>🎬 Animation:</strong> <?= ucfirst($currentSettings['animation']) ?></p>
                    <p><strong>⏱️ Duration:</strong> <?= $currentSettings['duration'] ?>ms</p>
                    <p><strong>🔄 Auto Hide:</strong> <?= $currentSettings['auto_hide'] ? 'Enabled' : 'Disabled' ?></p>
                    <p><strong>📊 Progress:</strong> <?= $currentSettings['show_progress'] ? 'Enabled' : 'Disabled' ?></p>
                    <p><strong>👆 Click Dismiss:</strong> <?= $currentSettings['click_dismiss'] ? 'Enabled' : 'Disabled' ?></p>
                    <p><strong>🔊 Sounds:</strong> <?= $currentSettings['enable_sounds'] ? 'Enabled' : 'Disabled' ?></p>
                </div>

                <div style="margin-top: 20px; padding: 15px; background: rgba(255,255,255,0.1); border-radius: 10px;">
                    <h4>💡 How it works:</h4>
                    <p style="font-size: 14px; line-height: 1.6;">
                        When you save settings, this page directly modifies the <code>ultimate-flash.php</code> file
                        to update the default values. All changes are permanent and will affect all users globally.
                    </p>
                </div>
            </div>
        </div>

        <!-- Demo Functions -->
        <div class="panel">
            <h3>🧪 Live Demo - Test All Functions</h3>
            <div class="demo-grid">
                <!-- Gaming Functions -->
                <div class="demo-section">
                    <h4>🎮 Gaming</h4>
                    <form method="post" style="display: contents;">
                        <input type="hidden" name="demo_action" value="quiz_victory">
                        <button type="submit" class="demo-btn achievement">🎉 Quiz Victory</button>
                    </form>
                    <form method="post" style="display: contents;">
                        <input type="hidden" name="demo_action" value="perfect_score">
                        <button type="submit" class="demo-btn achievement">🌟 Perfect Score</button>
                    </form>
                    <form method="post" style="display: contents;">
                        <input type="hidden" name="demo_action" value="daily_coins">
                        <button type="submit" class="demo-btn success">🪙 Daily Coins</button>
                    </form>
                    <form method="post" style="display: contents;">
                        <input type="hidden" name="demo_action" value="streak">
                        <button type="submit" class="demo-btn achievement">🔥 Streak</button>
                    </form>
                    <form method="post" style="display: contents;">
                        <input type="hidden" name="demo_action" value="badge">
                        <button type="submit" class="demo-btn achievement">🎖️ Badge</button>
                    </form>
                    <form method="post" style="display: contents;">
                        <input type="hidden" name="demo_action" value="rank_up">
                        <button type="submit" class="demo-btn achievement">👑 Rank Up</button>
                    </form>
                </div>

                <!-- Social Functions -->
                <div class="demo-section">
                    <h4>👥 Social</h4>
                    <form method="post" style="display: contents;">
                        <input type="hidden" name="demo_action" value="welcome_back">
                        <button type="submit" class="demo-btn success">👋 Welcome Back</button>
                    </form>
                    <form method="post" style="display: contents;">
                        <input type="hidden" name="demo_action" value="new_follower">
                        <button type="submit" class="demo-btn info">👥 New Follower</button>
                    </form>
                    <form method="post" style="display: contents;">
                        <input type="hidden" name="demo_action" value="post_liked">
                        <button type="submit" class="demo-btn info">❤️ Post Liked</button>
                    </form>
                    <form method="post" style="display: contents;">
                        <input type="hidden" name="demo_action" value="viral_post">
                        <button type="submit" class="demo-btn achievement">🎉 Viral Post</button>
                    </form>
                </div>

                <!-- E-commerce Functions -->
                <div class="demo-section">
                    <h4>🛒 E-commerce</h4>
                    <form method="post" style="display: contents;">
                        <input type="hidden" name="demo_action" value="add_to_cart">
                        <button type="submit" class="demo-btn success">🛒 Add to Cart</button>
                    </form>
                    <form method="post" style="display: contents;">
                        <input type="hidden" name="demo_action" value="order_confirmed">
                        <button type="submit" class="demo-btn success">✅ Order Confirmed</button>
                    </form>
                    <form method="post" style="display: contents;">
                        <input type="hidden" name="demo_action" value="order_shipped">
                        <button type="submit" class="demo-btn info">🚚 Order Shipped</button>
                    </form>
                    <form method="post" style="display: contents;">
                        <input type="hidden" name="demo_action" value="flash_sale">
                        <button type="submit" class="demo-btn warning">🔥 Flash Sale</button>
                    </form>
                </div>

                <!-- Freelance Functions -->
                <div class="demo-section">
                    <h4>💼 Freelance</h4>
                    <form method="post" style="display: contents;">
                        <input type="hidden" name="demo_action" value="job_awarded">
                        <button type="submit" class="demo-btn achievement">🎉 Job Awarded</button>
                    </form>
                    <form method="post" style="display: contents;">
                        <input type="hidden" name="demo_action" value="payment_received">
                        <button type="submit" class="demo-btn success">💰 Payment</button>
                    </form>
                </div>

                <!-- Learning Functions -->
                <div class="demo-section">
                    <h4>📚 Learning</h4>
                    <form method="post" style="display: contents;">
                        <input type="hidden" name="demo_action" value="course_completed">
                        <button type="submit" class="demo-btn achievement">🎓 Course Complete</button>
                    </form>
                    <form method="post" style="display: contents;">
                        <input type="hidden" name="demo_action" value="skill_mastered">
                        <button type="submit" class="demo-btn achievement">🧠 Skill Mastered</button>
                    </form>
                </div>

                <!-- System Functions -->
                <div class="demo-section">
                    <h4>🔧 System</h4>
                    <form method="post" style="display: contents;">
                        <input type="hidden" name="demo_action" value="email_sent">
                        <button type="submit" class="demo-btn success">📧 Email Sent</button>
                    </form>
                    <form method="post" style="display: contents;">
                        <input type="hidden" name="demo_action" value="file_uploaded">
                        <button type="submit" class="demo-btn success">📤 File Upload</button>
                    </form>
                    <form method="post" style="display: contents;">
                        <input type="hidden" name="demo_action" value="file_downloaded">
                        <button type="submit" class="demo-btn info">📥 File Download</button>
                    </form>
                    <form method="post" style="display: contents;">
                        <input type="hidden" name="demo_action" value="data_synced">
                        <button type="submit" class="demo-btn success">🔄 Data Sync</button>
                    </form>
                    <form method="post" style="display: contents;">
                        <input type="hidden" name="demo_action" value="backup_created">
                        <button type="submit" class="demo-btn success">📊 Backup</button>
                    </form>
                    <form method="post" style="display: contents;">
                        <input type="hidden" name="demo_action" value="system_updated">
                        <button type="submit" class="demo-btn achievement">🔄 System Update</button>
                    </form>
                    <form method="post" style="display: contents;">
                        <input type="hidden" name="demo_action" value="performance_alert">
                        <button type="submit" class="demo-btn warning">📈 Performance</button>
                    </form>
                    <form method="post" style="display: contents;">
                        <input type="hidden" name="demo_action" value="realtime_notification">
                        <button type="submit" class="demo-btn info">🔔 Realtime</button>
                    </form>
                </div>

                <!-- Advanced UI -->
                <div class="demo-section">
                    <h4>🎨 Advanced UI</h4>
                    <form method="post" style="display: contents;">
                        <input type="hidden" name="demo_action" value="progress">
                        <button type="submit" class="demo-btn info">📊 Progress</button>
                    </form>
                    <form method="post" style="display: contents;">
                        <input type="hidden" name="demo_action" value="loading">
                        <button type="submit" class="demo-btn info">⏳ Loading</button>
                    </form>
                    <form method="post" style="display: contents;">
                        <input type="hidden" name="demo_action" value="prompt">
                        <button type="submit" class="demo-btn warning">✏️ Prompt</button>
                    </form>
                    <form method="post" style="display: contents;">
                        <input type="hidden" name="demo_action" value="date_picker">
                        <button type="submit" class="demo-btn info">📅 Date Picker</button>
                    </form>
                    <form method="post" style="display: contents;">
                        <input type="hidden" name="demo_action" value="list_selector">
                        <button type="submit" class="demo-btn info">📋 List Select</button>
                    </form>
                    <form method="post" style="display: contents;">
                        <input type="hidden" name="demo_action" value="color_picker">
                        <button type="submit" class="demo-btn info">🎨 Color Pick</button>
                    </form>
                    <form method="post" style="display: contents;">
                        <input type="hidden" name="demo_action" value="file_selector">
                        <button type="submit" class="demo-btn info">📁 File Select</button>
                    </form>
                    <form method="post" style="display: contents;">
                        <input type="hidden" name="demo_action" value="image_preview">
                        <button type="submit" class="demo-btn info">🖼️ Image</button>
                    </form>
                </div>

                <!-- More Gaming -->
                <div class="demo-section">
                    <h4>🎮 More Gaming</h4>
                    <form method="post" style="display: contents;">
                        <input type="hidden" name="demo_action" value="quiz_completed">
                        <button type="submit" class="demo-btn success">📝 Quiz Done</button>
                    </form>
                    <form method="post" style="display: contents;">
                        <input type="hidden" name="demo_action" value="correct_answer">
                        <button type="submit" class="demo-btn success">✅ Correct</button>
                    </form>
                    <form method="post" style="display: contents;">
                        <input type="hidden" name="demo_action" value="wrong_answer">
                        <button type="submit" class="demo-btn error">❌ Wrong</button>
                    </form>
                    <form method="post" style="display: contents;">
                        <input type="hidden" name="demo_action" value="level_up">
                        <button type="submit" class="demo-btn achievement">⬆️ Level Up</button>
                    </form>
                    <form method="post" style="display: contents;">
                        <input type="hidden" name="demo_action" value="points_earned">
                        <button type="submit" class="demo-btn success">⭐ Points</button>
                    </form>
                    <form method="post" style="display: contents;">
                        <input type="hidden" name="demo_action" value="bonus_coins">
                        <button type="submit" class="demo-btn success">🎁 Bonus</button>
                    </form>
                    <form method="post" style="display: contents;">
                        <input type="hidden" name="demo_action" value="game_over">
                        <button type="submit" class="demo-btn warning">🎮 Game Over</button>
                    </form>
                    <form method="post" style="display: contents;">
                        <input type="hidden" name="demo_action" value="daily_reward">
                        <button type="submit" class="demo-btn achievement">🎁 Daily Reward</button>
                    </form>
                </div>

                <!-- Security -->
                <div class="demo-section">
                    <h4>🔒 Security</h4>
                    <form method="post" style="display: contents;">
                        <input type="hidden" name="demo_action" value="account_locked">
                        <button type="submit" class="demo-btn error">🔒 Account Lock</button>
                    </form>
                    <form method="post" style="display: contents;">
                        <input type="hidden" name="demo_action" value="account_unlocked">
                        <button type="submit" class="demo-btn success">🔓 Account Unlock</button>
                    </form>
                    <form method="post" style="display: contents;">
                        <input type="hidden" name="demo_action" value="password_changed">
                        <button type="submit" class="demo-btn success">🔑 Password</button>
                    </form>
                </div>

                <!-- CRUD Operations -->
                <div class="demo-section">
                    <h4>🔧 CRUD Operations</h4>
                    <form method="post" style="display: contents;">
                        <input type="hidden" name="demo_action" value="data_added">
                        <button type="submit" class="demo-btn success">➕ Add</button>
                    </form>
                    <form method="post" style="display: contents;">
                        <input type="hidden" name="demo_action" value="data_edited">
                        <button type="submit" class="demo-btn success">✏️ Edit</button>
                    </form>
                    <form method="post" style="display: contents;">
                        <input type="hidden" name="demo_action" value="data_updated">
                        <button type="submit" class="demo-btn success">🔄 Update</button>
                    </form>
                    <form method="post" style="display: contents;">
                        <input type="hidden" name="demo_action" value="data_created">
                        <button type="submit" class="demo-btn success">📋 Create</button>
                    </form>
                    <form method="post" style="display: contents;">
                        <input type="hidden" name="demo_action" value="data_imported">
                        <button type="submit" class="demo-btn info">🗂️ Import</button>
                    </form>
                    <form method="post" style="display: contents;">
                        <input type="hidden" name="demo_action" value="data_exported">
                        <button type="submit" class="demo-btn info">📤 Export</button>
                    </form>
                    <form method="post" style="display: contents;">
                        <input type="hidden" name="demo_action" value="data_restored">
                        <button type="submit" class="demo-btn success">🔄 Restore</button>
                    </form>
                    <form method="post" style="display: contents;">
                        <input type="hidden" name="demo_action" value="data_published">
                        <button type="submit" class="demo-btn success">📊 Publish</button>
                    </form>
                </div>

                <!-- Authentication -->
                <div class="demo-section">
                    <h4>🔐 Authentication</h4>
                    <form method="post" style="display: contents;">
                        <input type="hidden" name="demo_action" value="registration_success">
                        <button type="submit" class="demo-btn achievement">📝 Registration</button>
                    </form>
                    <form method="post" style="display: contents;">
                        <input type="hidden" name="demo_action" value="login_success">
                        <button type="submit" class="demo-btn success">🔐 Login</button>
                    </form>
                    <form method="post" style="display: contents;">
                        <input type="hidden" name="demo_action" value="logout_success">
                        <button type="submit" class="demo-btn info">🚪 Logout</button>
                    </form>
                </div>

                <!-- Basic Functions -->
                <div class="demo-section">
                    <h4>📢 Basic</h4>
                    <form method="post" style="display: contents;">
                        <input type="hidden" name="demo_action" value="success">
                        <button type="submit" class="demo-btn success">✅ Success</button>
                    </form>
                    <form method="post" style="display: contents;">
                        <input type="hidden" name="demo_action" value="error">
                        <button type="submit" class="demo-btn error">❌ Error</button>
                    </form>
                    <form method="post" style="display: contents;">
                        <input type="hidden" name="demo_action" value="warning">
                        <button type="submit" class="demo-btn warning">⚠️ Warning</button>
                    </form>
                    <form method="post" style="display: contents;">
                        <input type="hidden" name="demo_action" value="info">
                        <button type="submit" class="demo-btn info">ℹ️ Info</button>
                    </form>
                </div>

                <!-- Advanced Engagement -->
                <div class="demo-section">
                    <h4>🎯 Engagement</h4>
                    <form method="post" style="display: contents;">
                        <input type="hidden" name="demo_action" value="daily_coin_summary">
                        <button type="submit" class="demo-btn achievement">💰 Daily Coins</button>
                    </form>
                    <form method="post" style="display: contents;">
                        <input type="hidden" name="demo_action" value="gaming_session">
                        <button type="submit" class="demo-btn achievement">🎮 Gaming Session</button>
                    </form>
                    <form method="post" style="display: contents;">
                        <input type="hidden" name="demo_action" value="progress_milestone">
                        <button type="submit" class="demo-btn info">📈 Progress</button>
                    </form>
                    <form method="post" style="display: contents;">
                        <input type="hidden" name="demo_action" value="weekly_summary">
                        <button type="submit" class="demo-btn achievement">🎊 Weekly Report</button>
                    </form>
                    <form method="post" style="display: contents;">
                        <input type="hidden" name="demo_action" value="smart_motivation">
                        <button type="submit" class="demo-btn info">🎯 Motivation</button>
                    </form>
                </div>

                <!-- Special Features -->
                <div class="demo-section">
                    <h4>🌟 Special</h4>
                    <form method="post" style="display: contents;">
                        <input type="hidden" name="demo_action" value="special_event">
                        <button type="submit" class="demo-btn warning">🎪 Special Event</button>
                    </form>
                    <form method="post" style="display: contents;">
                        <input type="hidden" name="demo_action" value="leaderboard_update">
                        <button type="submit" class="demo-btn achievement">🏆 Leaderboard</button>
                    </form>
                    <form method="post" style="display: contents;">
                        <input type="hidden" name="demo_action" value="customization_unlocked">
                        <button type="submit" class="demo-btn achievement">🎨 Customization</button>
                    </form>
                    <form method="post" style="display: contents;">
                        <input type="hidden" name="demo_action" value="feature_discovery">
                        <button type="submit" class="demo-btn info">🌟 Feature Discovery</button>
                    </form>
                </div>

                <!-- Modal Functions -->
                <div class="demo-section">
                    <h4>🪟 Modals</h4>
                    <form method="post" style="display: contents;">
                        <input type="hidden" name="demo_action" value="confirm">
                        <button type="submit" class="demo-btn warning">❓ Confirm</button>
                    </form>
                    <form method="post" style="display: contents;">
                        <input type="hidden" name="demo_action" value="alert">
                        <button type="submit" class="demo-btn error">🚨 Alert</button>
                    </form>
                    <form method="post" style="display: contents;">
                        <input type="hidden" name="demo_action" value="modal">
                        <button type="submit" class="demo-btn info">📋 Modal</button>
                    </form>
                </div>
            </div>
        </div>

        <!-- Statistics -->
        <div class="stats">
            <h3>📊 System Information</h3>
            <?php $stats = UltimateFlash::getStats(); ?>
            <p><strong>Total Functions:</strong> 135+ | <strong>Demo Buttons:</strong> 120+ | <strong>File Size:</strong> <?= round(filesize(__DIR__ . '/ultimate-flash.php') / 1024, 1) ?>KB</p>
            <p><strong>Categories:</strong> Gaming, Social, E-commerce, Freelance, Learning, System, Engagement, Special, CRUD, Auth, UI, Security</p>
            <p><strong>Features:</strong> Analytics, Smart Positioning, Touch Gestures, Custom Themes, Mobile Optimization</p>
            <p><strong>PHP Version:</strong> <?= PHP_VERSION ?> | <strong>Memory Usage:</strong> <?= round(memory_get_usage() / 1024 / 1024, 2) ?>MB</p>
            <p><strong>File Writable:</strong> <?= is_writable(__DIR__ . '/ultimate-flash.php') ? '✅ Yes' : '❌ No' ?> | <strong>Sounds:</strong> <?= $currentSettings['enable_sounds'] ? '🔊 Enabled' : '🔇 Disabled' ?></p>
        </div>
    </div>

    <!-- Render Flash Messages -->
    <?= UltimateFlash::render() ?>

    <script>
        console.log('🚀 Ultimate Flash Messages - Setup & Demo Interface Loaded');
        console.log('Current Settings:', <?= json_encode($currentSettings) ?>);

        // Auto-refresh page after form submission to show updated settings
        if (window.location.hash === '#saved') {
            window.location.hash = '';
            window.location.reload();
        }
    </script>
</body>
</html>
